package com.vinkey.restapi.communication.deviation;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.communication.deviation.dto.DeviationChange;
import com.vinkey.restapi.communication.deviation.dto.DeviationCreate;
import com.vinkey.restapi.communication.deviation.dto.DeviationRead;
import com.vinkey.restapi.communication.deviation.dto.DeviationUpdate;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/deviations")
public class DeviationController {
  private final DeviationService deviationService;
  private final DeviationHistoryService deviationHistoryService;
  private final DeviationMapper deviationMapper;
  private final FileService fileService;

  public DeviationController(
      DeviationService deviationService,
      DeviationHistoryService deviationHistoryService,
      FileService fileService) {
    this.deviationService = deviationService;
    this.deviationHistoryService = deviationHistoryService;
    this.fileService = fileService;
    this.deviationMapper = DeviationMapper.INSTANCE;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public DeviationRead createDeviation(
      @Valid @RequestBody DeviationCreate deviationCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Long tenantId = jwtDetails.getTenantId();
    Deviation referenceDeviation =
        deviationMapper.deviationCreateToDeviation(deviationCreate, tenantId);
    Long deviationId = deviationService.create(referenceDeviation);
    return deviationMapper.deviationToDeviationRead(deviationService.read(deviationId, tenantId));
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public DeviationRead getDeviation(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Deviation deviation = deviationService.read(id, jwtDetails.getTenantId());
    deviation.setFiles(
        fileService.setUrls(deviation.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return deviationMapper.deviationToDeviationRead(deviation);
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<DeviationRead> getDeviations(
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) Long pathGroupId,
      @RequestParam(required = false) DeviationStatus status,
      @RequestParam(required = false) DeviationStatus statusNot,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageNumber,
      @RequestParam(required = false) Long pageSize,
      @RequestParam(required = false) Long locationId,
      @RequestParam(required = false) Long ancestorLocationId,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Page<Deviation> deviations =
        deviationService.readPage(
            jwtDetails.getTenantId(),
            groupId,
            ancestorGroupId,
            pathGroupId,
            status,
            statusNot,
            createdBy,
            candidateGroups,
            filter,
            search,
            sort,
            pageNumber,
            pageSize,
            locationId,
            ancestorLocationId);

    return deviationMapper.paginatedDeviationsToPaginatedDeviationReads(deviations);
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public DeviationRead updateDeviation(
      @PathVariable Long id,
      @Valid @RequestBody DeviationUpdate deviationUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Deviation deviation = deviationService.read(id, jwtDetails.getTenantId());
    deviationMapper.updateDeviationFromDeviationUpdate(deviation, deviationUpdate);
    Deviation updatedDeviation = deviationService.update(deviation);
    return deviationMapper.deviationToDeviationRead(updatedDeviation);
  }

  @GetMapping("/{id}/deletable")
  @ResponseStatus(HttpStatus.OK)
  public Deletable getDeviationDeletable(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Deviation deviation = deviationService.read(id, jwtDetails.getTenantId());
    Deletable deletable = new Deletable();
    deletable.setDeletable(deviationService.checkDeletable(deviation));
    return deletable;
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteDeviation(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Deviation deviation = deviationService.read(id, jwtDetails.getTenantId());
    deviationService.delete(deviation);
  }

  @PostMapping("/{id}/cancel")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void cancelDeviation(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Deviation deviation = deviationService.read(id, jwtDetails.getTenantId());
    deviationService.cancelDeviation(deviation);
  }

  @GetMapping(value = "/{id}/history")
  @ResponseBody
  public List<DeviationChange> getHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return deviationHistoryService.getHistory(id, jwtDetails.getTenantId());
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generateDeviationPdf(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    Deviation deviation = deviationService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION,
        "attachment; filename=Deviation " + deviation.getSid() + ".pdf");
    deviationService.generatePdf(deviation, timeZone, response.getOutputStream());
  }
}
