package com.vinkey.restapi.communication.deviation;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.communication.deviation.exception.DeviationLockedException;
import com.vinkey.restapi.communication.deviation.exception.DeviationNotDeletableException;
import com.vinkey.restapi.communication.deviation.exception.DeviationNotFoundException;
import com.vinkey.restapi.communication.deviation.notification.event.DeviationCanceledEvent;
import com.vinkey.restapi.communication.deviation.notification.event.DeviationClosedEvent;
import com.vinkey.restapi.communication.deviation.notification.event.DeviationUpdatedEvent;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.permittowork.workpermit.PDFException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class DeviationService {
  private final ApplicationEventPublisher applicationEventPublisher;
  private final DeviationRepository deviationRepository;
  private final RuntimeService runtimeService;
  private final ITemplateEngine templateEngine;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public DeviationService(
      ApplicationEventPublisher applicationEventPublisher,
      DeviationRepository deviationRepository,
      RuntimeService runtimeService,
      ITemplateEngine templateEngine) {
    this.applicationEventPublisher = applicationEventPublisher;
    this.deviationRepository = deviationRepository;
    this.runtimeService = runtimeService;
    this.templateEngine = templateEngine;
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#deviation.getGroup().getId(), authentication.principal.userId, 'DEVIATION_CREATE')")
  @Transactional
  public Long create(Deviation deviation) {
    String tenantId = deviation.getTenant().getId().toString();
    ProcessInstanceBuilder processInstanceBuilder = runtimeService.createProcessInstanceBuilder();
    processInstanceBuilder.processDefinitionKey("devationProcess");
    processInstanceBuilder.tenantId(tenantId);

    ProcessInstance processInstance = processInstanceBuilder.start();

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(processInstance.getId());
    processInstanceCustom.setProcessInstanceId(processInstance.getProcessInstanceId());
    deviation.setProcessInstance(processInstanceCustom);

    deviationRepository.save(deviation);

    Map<String, Object> variables = new HashMap<String, Object>();

    variables.put("groupId", deviation.getGroup().getId());
    variables.put("id", deviation.getId());
    variables.put("sid", deviation.getSid());
    variables.put("name", deviation.getDescription());
    variables.put("createdBy", deviation.getCreatedBy().getId());
    variables.put("ready", true);

    runtimeService.setVariables(processInstance.getId(), variables);

    return deviation.getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#deviation.getGroup().getId(), authentication.principal.userId, 'DEVIATION_UPDATE')")
  @Transactional
  public Deviation update(Deviation deviation) {
    if (Boolean.TRUE.equals(deviation.getLocked())) {
      throw new DeviationLockedException(deviation.getId());
    }
    deviationRepository.save(deviation);
    applicationEventPublisher.publishEvent(new DeviationUpdatedEvent(deviation.getId()));
    return this.read(deviation.getId(), deviation.getTenant().getId());
  }

  @PostAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPathPrivilege(returnObject.group.id, authentication.principal.userId, 'DEVIATION_READ')
    """)
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public Deviation read(Long id, Long tenantId) {
    Optional<Deviation> deviation = deviationRepository.findByIdAndTenantId(id, tenantId);
    if (deviation.isEmpty()) {
      throw new DeviationNotFoundException(id);
    }
    return deviation.get();
  }

  @PreAuthorize(
      """
            hasRole('TENANT_ADMIN')
            OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'DEVIATION_READ')
            OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'DEVIATION_READ')
            OR @membershipService.hasPrivilege(#pathGroupId, authentication.principal.userId, 'DEVIATION_READ')
            OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "pathGroupId"), authentication.principal.userId, 'DEVIATION_READ')
            OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'DEVIATION_READ')
            OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"), authentication.principal.userId, 'DEVIATION_READ')
        """)
  public Page<Deviation> readPage(
      Long tenantId,
      Long groupId,
      Long ancestorGroupId,
      Long pathGroupId,
      DeviationStatus status,
      DeviationStatus statusNot,
      Long createdBy,
      List<String> candidateGroups,
      String filter,
      String search,
      String sort,
      Long pageNumber,
      Long pageSize,
      Long locationId,
      Long ancestorLocationId) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, Deviation_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(sort, DeviationSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    DeviationSpecificationBuilder builder =
        new DeviationSpecificationBuilder()
            .distinct()
            .tenantId(tenantId)
            .groupId(groupId)
            .ancestorGroupId(ancestorGroupId)
            .pathGroupId(pathGroupId)
            .status(status)
            .statusNot(statusNot)
            .createdBy(createdBy)
            .candidateGroups(candidateGroups)
            .locationId(locationId)
            .ancestorLocationId(ancestorLocationId)
            .search(search);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }
    return deviationRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#deviation.getGroup().getId(), authentication.principal.userId, 'DEVIATION_DELETE')")
  public Boolean checkDeletable(Deviation deviation) {
    return !deviationRepository.existsAnyRelationsById(deviation.getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#deviation.getGroup().getId(), authentication.principal.userId, 'DEVIATION_DELETE')")
  public void delete(Deviation deviation) {
    if (!checkDeletable(deviation)) {
      throw new DeviationNotDeletableException(deviation.getId());
    }
    deviationRepository.delete(deviation);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#deviation.getGroup().getId(), authentication.principal.userId, 'DEVIATION_CANCEL') or #deviation.getCreatedBy().getId().equals(authentication.principal.userId)")
  @Transactional
  public void cancelDeviation(Deviation deviation) {
    deviation.setLocked(true);
    deviation.setStatus(DeviationStatus.CANCELED);
    deviationRepository.save(deviation);
    if (deviation.getProcessInstance() != null) {
      runtimeService.deleteProcessInstance(
          deviation.getProcessInstance().getId(), "Deviation canceled by user");
    }
    applicationEventPublisher.publishEvent(new DeviationCanceledEvent(deviation.getId()));
  }

  @PreAuthorize(
      """
            hasRole('TENANT_ADMIN')
            OR @membershipService.hasPrivilege(#deviation.group.id, authentication.principal.userId, DEVIATION_READ)
      """)
  public Context generatePdfContext(Deviation deviation, String timeZone) {
    Context context = new Context();
    context.setVariable("deviation", deviation);
    context.setVariable("timeZone", timeZone);
    return context;
  }

  @PreAuthorize(
      """
            hasRole('TENANT_ADMIN')
            OR @membershipService.hasPrivilege(#deviation.group.id, authentication.principal.userId, DEVIATION_READ)
      """)
  public void generatePdf(Deviation deviation, String timeZone, OutputStream out) {
    Context context = this.generatePdfContext(deviation, timeZone);
    String html = templateEngine.process("pdf/deviation.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();

      builder.withUri("deviation.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }

  @Transactional
  public void lock(Long id, Long tenantId) {
    Deviation deviation = read(id, tenantId);
    deviation.setLocked(true);
    deviationRepository.save(deviation);
  }

  @Transactional
  public void close(Long id, Long tenantId, String reason) {
    Deviation deviation = read(id, tenantId);
    deviation.setStatus(DeviationStatus.CLOSED);
    deviation.setCloseReason(reason);
    deviationRepository.save(deviation);
    applicationEventPublisher.publishEvent(new DeviationClosedEvent(id));
  }
}
