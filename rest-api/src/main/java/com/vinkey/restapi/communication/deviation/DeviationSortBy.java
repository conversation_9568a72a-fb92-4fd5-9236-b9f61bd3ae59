package com.vinkey.restapi.communication.deviation;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum DeviationSortBy implements SortByEnum {
  ID(Deviation_.ID),
  SID(Deviation_.SID),
  DESCRIPTION(Deviation_.DESCRIPTION),
  STATUS(Deviation_.STATUS),
  DATE(Deviation_.CREATION_DATE);

  private final String field;

  DeviationSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
