package com.vinkey.restapi.communication.instruction;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.communication.instruction.dto.InstructionChange;
import com.vinkey.restapi.communication.instruction.dto.InstructionCreate;
import com.vinkey.restapi.communication.instruction.dto.InstructionRead;
import com.vinkey.restapi.communication.instruction.dto.InstructionUpdate;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/instructions")
public class InstructionController {
  private final InstructionService instructionService;
  private final InstructionHistoryService instructionHistroyService;
  private final InstructionMapper instructionMapper;
  private final FileService fileService;

  public InstructionController(
      InstructionService instructionService,
      InstructionHistoryService instructionHistroyService,
      FileService fileService) {
    this.instructionService = instructionService;
    this.instructionHistroyService = instructionHistroyService;
    this.fileService = fileService;
    this.instructionMapper = InstructionMapper.INSTANCE;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public InstructionRead createInstruction(
      @Valid @RequestBody InstructionCreate instructionCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Long tenantId = jwtDetails.getTenantId();

    Instruction instruction =
        instructionMapper.instructionCreateToInstruction(instructionCreate, tenantId);

    return instructionMapper.instructionToInstructionRead(
        instructionService.read(instructionService.create(instruction), jwtDetails.getTenantId()));
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<InstructionRead> getInstructions(
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) Long pathGroupId,
      @RequestParam(required = false) Long startDateLte,
      @RequestParam(required = false) Long startDateGte,
      @RequestParam(required = false) Long endDateLte,
      @RequestParam(required = false) Long endDateGte,
      @RequestParam(required = false) Boolean endDateNotNull,
      @RequestParam(required = false) Boolean endDateNull,
      @RequestParam(required = false) InstructionStatus status,
      @RequestParam(required = false) InstructionStatus statusNot,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageNumber,
      @RequestParam(required = false) Long pageSize,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    return instructionMapper.paginatedInstructionsToPaginatedInstructionReads(
        instructionService.readPage(
            jwtDetails.getTenantId(),
            groupId,
            ancestorGroupId,
            pathGroupId,
            startDateLte,
            startDateGte,
            endDateLte,
            endDateGte,
            endDateNotNull,
            endDateNull,
            status,
            statusNot,
            candidateGroups,
            createdBy,
            filter,
            search,
            sort,
            pageNumber,
            pageSize));
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public InstructionRead getInstruction(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Instruction instruction = instructionService.read(id, jwtDetails.getTenantId());
    instruction.setFiles(
        fileService.setUrls(instruction.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return instructionMapper.instructionToInstructionRead(instruction);
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public InstructionRead updateInstruction(
      @PathVariable Long id,
      @Valid @RequestBody InstructionUpdate instructionUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Instruction instruction = instructionService.read(id, jwtDetails.getTenantId());

    instructionMapper.updateInstructionFromInstructionUpdate(instruction, instructionUpdate);

    return instructionMapper.instructionToInstructionRead(instructionService.update(instruction));
  }

  @GetMapping("/{id}/deletable")
  @ResponseStatus(HttpStatus.OK)
  public Deletable getDeletable(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Instruction instruction = instructionService.read(id, jwtDetails.getTenantId());

    Deletable deletable = new Deletable();
    deletable.setDeletable(instructionService.checkDeletable(instruction));
    return deletable;
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteInstruction(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Instruction instruction = instructionService.read(id, jwtDetails.getTenantId());
    instructionService.delete(instruction);
  }

  @GetMapping("/{id}/history")
  @ResponseStatus(HttpStatus.OK)
  public List<InstructionChange> getHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return instructionHistroyService.getHistory(id, jwtDetails.getTenantId());
  }

  @PostMapping("/{id}/cancel")
  @ResponseStatus(HttpStatus.OK)
  public void cancelInstruction(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Instruction instruction = instructionService.read(id, jwtDetails.getTenantId());
    instructionService.cancel(instruction);
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generateInstructionPdf(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    Instruction instruction = instructionService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION,
        "attachment; filename=Instruction " + instruction.getSid() + ".pdf");
    instructionService.generatePdf(instruction, timeZone, response.getOutputStream());
  }
}
