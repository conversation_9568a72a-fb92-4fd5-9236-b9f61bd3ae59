package com.vinkey.restapi.communication.instruction;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.communication.instruction.exception.InstructionLockedException;
import com.vinkey.restapi.communication.instruction.exception.InstructionNotDeletableException;
import com.vinkey.restapi.communication.instruction.exception.InstructionNotFoundException;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionActivatedEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionCanceledEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionClosedEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionPlannedEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionUpdatedEvent;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.permittowork.workpermit.PDFException;
import java.io.IOException;
import java.io.OutputStream;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.stream.Collectors;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class InstructionService {
  private final InstructionRepository instructionRepository;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final ITemplateEngine templateEngine;
  private final RuntimeService runtimeService;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public InstructionService(
      InstructionRepository instructionRepository,
      ApplicationEventPublisher applicationEventPublisher,
      ITemplateEngine templateEngine,
      RuntimeService runtimeService) {
    this.instructionRepository = instructionRepository;
    this.applicationEventPublisher = applicationEventPublisher;
    this.templateEngine = templateEngine;
    this.runtimeService = runtimeService;
  }

  @PostAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPathPrivilege(returnObject.group.id, authentication.principal.userId, 'INSTRUCTION_READ')")
  public Instruction read(Long id, Long tenantId) {
    Optional<Instruction> instruction = instructionRepository.findByIdAndTenantId(id, tenantId);

    if (instruction.isEmpty()) {
      throw new InstructionNotFoundException(id);
    }

    return instruction.get();
  }

  public Long retrieveFilterKeyValue(List<SpecFilterCriteria> decodedFilter, String key) {
    if (decodedFilter == null) {
      return null;
    }

    List<Long> allEntries =
        decodedFilter.stream()
            .filter(f -> f.getKey().equals(key))
            .map(f -> Long.valueOf((String) f.getValue()))
            .distinct()
            .collect(Collectors.toList());
    if (allEntries.isEmpty()) {
      return null;
    }
    return allEntries.get(0);
  }

  @PreAuthorize(
      """
          hasRole('TENANT_ADMIN')
          OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'INSTRUCTION_READ')
          OR @membershipService.hasPrivilege(@instructionService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'INSTRUCTION_READ')
          OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'INSTRUCTION_READ')
          OR @membershipService.hasPrivilege(@instructionService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"), authentication.principal.userId, 'INSTRUCTION_READ')
          OR @membershipService.hasPrivilege(#pathGroupId, authentication.principal.userId, 'INSTRUCTION_READ')
          OR @membershipService.hasPrivilege(@instructionService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "pathGroupId"), authentication.principal.userId, 'INSTRUCTION_READ')
      """)
  public Page<Instruction> readPage(
      Long tenantId,
      Long groupId,
      Long ancestorGroupId,
      Long pathGroupId,
      Long startTimeLte,
      Long startTimeGte,
      Long endTimeLte,
      Long endTimeGte,
      Boolean endTimeNotNull,
      Boolean endTimeNull,
      InstructionStatus status,
      InstructionStatus statusNot,
      List<String> candidateGroups,
      Long createdBy,
      String filter,
      String search,
      String sort,
      Long pageNumber,
      Long pageSize) {

    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, "startTime");
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(sort, InstructionSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable =
        PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    InstructionSpecificationBuilder builder =
        new InstructionSpecificationBuilder()
            .distinct()
            .tenant(tenantId)
            .group(groupId)
            .ancestorGroup(ancestorGroupId)
            .pathGroup(pathGroupId)
            .startDateGte(startTimeGte)
            .startDateLte(startTimeLte)
            .endDateGte(endTimeGte)
            .endDateLte(endTimeLte)
            .status(status)
            .statusNot(statusNot)
            .createdBy(createdBy)
            .candidateGroups(candidateGroups)
            .search(search);

    if (endTimeNotNull != null && endTimeNotNull) {
      builder.endDateNotNull();
    }

    if (endTimeNull != null && endTimeNull) {
      builder.endDateNull();
    }

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }

    return instructionRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      """
          hasRole('TENANT_ADMIN')
          OR @membershipService.hasPrivilege(#newInstruction.group.id, authentication.principal.userId, 'INSTRUCTION_CREATE')
      """)
  @Transactional
  public Long create(Instruction newInstruction) {
    Long tenantId = newInstruction.getTenant().getId();
    ProcessInstanceBuilder instanceBuilder = runtimeService.createProcessInstanceBuilder();
    instanceBuilder.processDefinitionKey("instructionProcess");
    instanceBuilder.tenantId(tenantId.toString());
    ProcessInstance processInstance = instanceBuilder.start();

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(processInstance.getId());
    processInstanceCustom.setProcessInstanceId(processInstance.getProcessInstanceId());
    newInstruction.setProcessInstance(processInstanceCustom);
    newInstruction.isPlanned();

    Instruction instruction = instructionRepository.save(newInstruction);

    Map<String, Object> variables = new HashMap<String, Object>();

    Boolean future = newInstruction.getStartTime() / 1000 > Instant.now().getEpochSecond() && true;

    Long startTimeWithOutMS = newInstruction.getStartTime() / 1000;
    Instant instant = Instant.ofEpochSecond(startTimeWithOutMS);
    String iso8601StartDate = instant.toString();

    if (instruction.getEndTime() != null) {
      Long endTimeWithOutMS = instruction.getEndTime() / 1000;
      instant = Instant.ofEpochSecond(endTimeWithOutMS);
      String iso8601EndDate = instant.toString();
      variables.put("endTime", iso8601EndDate);
    } else {
      variables.put("endTime", "P1000Y");
    }

    variables.put("future", future);
    variables.put("startTime", iso8601StartDate);
    variables.put("groupId", instruction.getGroup().getId());
    variables.put("id", newInstruction.getId());

    variables.put("sid", newInstruction.getSid());
    variables.put("name", newInstruction.getDescription());
    variables.put("createdBy", instruction.getCreatedBy().getId());
    variables.put("ready", true);

    runtimeService.setVariables(processInstance.getId(), variables);

    return instruction.getId();
  }

  @PreAuthorize(
      """
          hasRole('TENANT_ADMIN')
          OR @membershipService.hasPrivilege(#newInstruction.group.id, authentication.principal.userId, 'INSTRUCTION_UPDATE')
      """)
  @Transactional
  public Instruction update(Instruction newInstruction) {
    if (Boolean.TRUE.equals(newInstruction.getLocked())) {
      throw new InstructionLockedException(newInstruction.getId());
    }

    Instruction oldInstruction = read(newInstruction.getId(), newInstruction.getTenant().getId());

    ChangeActivityStateBuilder builder = null;

    String processInstanceId = newInstruction.getProcessInstance().getId();
    Boolean resetProcess = false;
    Map<String, Object> variables = new HashMap<>();

    if (!Objects.equals(oldInstruction.getStartTime(), newInstruction.getStartTime())) {
      Long startTimeWithOutMS = newInstruction.getStartTime() / 1000;
      Instant instant = Instant.ofEpochSecond(startTimeWithOutMS);
      String iso8601StartDate = instant.toString();
      variables.put("startTime", iso8601StartDate);
      resetProcess = true;
    }

    if (!Objects.equals(oldInstruction.getEndTime(), newInstruction.getEndTime())) {
      if (newInstruction.getEndTime() != null) {
        Long endTimeWithOutMS = newInstruction.getEndTime() / 1000;
        Instant instant = Instant.ofEpochSecond(endTimeWithOutMS);
        String iso8601EndDate = instant.toString();
        variables.put("endTime", iso8601EndDate);
      } else {
        variables.put("endTime", "P1000Y");
      }
      resetProcess = true;
    }

    Boolean future = newInstruction.getStartTime() / 1000 > Instant.now().getEpochSecond() && true;
    variables.put("future", future);

    runtimeService.setVariables(processInstanceId, variables);

    if (resetProcess) {
      List<Execution> execution =
          runtimeService.createExecutionQuery().processInstanceId(processInstanceId).list();
      String executionId =
          execution.stream().filter(e -> e.getActivityId() != null).findFirst().get().getId();

      builder = runtimeService.createChangeActivityStateBuilder();
      builder.processInstanceId(processInstanceId);
      builder.moveExecutionToActivityId(executionId, "startEvent");
    }

    instructionRepository.save(newInstruction);

    if (builder != null) {
      builder.changeState();
    }

    variables.clear();
    variables.put("ready", true);
    runtimeService.setVariables(processInstanceId, variables);

    applicationEventPublisher.publishEvent(new InstructionUpdatedEvent(newInstruction.getId()));

    return newInstruction;
  }

  @PreAuthorize(
      """
          hasRole('TENANT_ADMIN')
          OR @membershipService.hasPrivilege(#instruction.group.id, authentication.principal.userId, 'INSTRUCTION_DELETE')
      """)
  public Boolean checkDeletable(Instruction instruction) {
    return true;
  }

  @PreAuthorize(
      """
          hasRole('TENANT_ADMIN')
          OR @membershipService.hasPrivilege(#instruction.group.id, authentication.principal.userId, 'INSTRUCTION_DELETE')
      """)
  public void delete(Instruction instruction) {
    if (!checkDeletable(instruction)) {
      throw new InstructionNotDeletableException(instruction.getId());
    }
    instructionRepository.delete(instruction);
  }

  @PreAuthorize(
      """
          hasRole('TENANT_ADMIN')
          OR @membershipService.hasPrivilege(#instruction.group.id, authentication.principal.userId, 'INSTRUCTION_CANCEL')
          OR #instruction.getCreatedBy().getId().equals(authentication.principal.userId)
      """)
  @Transactional
  public void cancel(Instruction instruction) {
    instruction.setLocked(true);
    instruction.setStatus(InstructionStatus.CANCELED);
    if (instruction.getProcessInstance() != null) {
      runtimeService.deleteProcessInstance(
          instruction.getProcessInstance().getId(), "Instruction canceled by user");
    }
    instructionRepository.save(instruction);
    applicationEventPublisher.publishEvent(new InstructionCanceledEvent(instruction.getId()));
  }

  @PreAuthorize(
      """
            hasRole('TENANT_ADMIN')
            OR @membershipService.hasPathPrivilege(#instruction.group.id, authentication.principal.userId, 'INSTRUCTION_READ')
      """)
  public Context generatePdfContext(Instruction instruction, String timeZone) {
    Context context = new Context();
    context.setVariable("instruction", instruction);
    context.setVariable("timeZone", timeZone);
    return context;
  }

  @PreAuthorize(
      """
            hasRole('TENANT_ADMIN')
            OR @membershipService.hasPathPrivilege(#instruction.group.id, authentication.principal.userId, 'INSTRUCTION_READ')
      """)
  public void generatePdf(Instruction instruction, String timeZone, OutputStream out) {
    Context context = this.generatePdfContext(instruction, timeZone);
    String html = templateEngine.process("pdf/instruction.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();

      builder.withUri("instruction.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }

  @Transactional
  public void activateInstruction(Long id, Long tenantId) {
    Instruction instruction = read(id, tenantId);
    if (instruction.getStatus().equals(InstructionStatus.ACTIVE)) return;

    instruction.setStatus(InstructionStatus.ACTIVE);
    instructionRepository.save(instruction);
    applicationEventPublisher.publishEvent(new InstructionActivatedEvent(instruction.getId()));
  }

  @Transactional
  public void closeInstruction(Long id, Long tenantId) {
    Instruction instruction = read(id, tenantId);
    if (instruction.getStatus().equals(InstructionStatus.CLOSED)) return;

    instruction.setStatus(InstructionStatus.CLOSED);
    instructionRepository.save(instruction);
    applicationEventPublisher.publishEvent(new InstructionClosedEvent(instruction.getId()));
  }

  @Transactional
  public void planInstruction(Long id, Long tenantId) {
    Instruction instruction = read(id, tenantId);
    if (instruction.getStatus().equals(InstructionStatus.PLANNED)) return;

    instruction.setStatus(InstructionStatus.PLANNED);
    instructionRepository.save(instruction);
    applicationEventPublisher.publishEvent(new InstructionPlannedEvent(instruction.getId()));
  }

  public void lockInstruction(Long id, Long tenantId) {
    Instruction instruction = read(id, tenantId);
    instruction.setLocked(true);
    instructionRepository.save(instruction);
  }
}
