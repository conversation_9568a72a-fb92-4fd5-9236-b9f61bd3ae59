package com.vinkey.restapi.communication.instruction;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum InstructionSortBy implements SortByEnum {
  ID(Instruction_.ID),
  SID(Instruction_.SID),
  DESCRIPTION(Instruction_.DESCRIPTION),
  STATUS(Instruction_.STATUS),
  START_TIME(Instruction_.START_TIME);

  private final String field;

  InstructionSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
