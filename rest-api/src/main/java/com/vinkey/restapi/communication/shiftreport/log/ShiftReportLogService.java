package com.vinkey.restapi.communication.shiftreport.log;

import com.vinkey.restapi.communication.shiftreport.ShiftReport;
import com.vinkey.restapi.communication.shiftreport.ShiftReportLockedException;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

@Service
public class ShiftReportLogService {
  private final ShiftReportLogRepository shiftReportLogRepository;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public ShiftReportLogService(ShiftReportLogRepository shiftReportLogRepository) {
    this.shiftReportLogRepository = shiftReportLogRepository;
  }

  @PostAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(returnObject.shiftReportSubTopic.id, authentication.principal.userId, 'SHIFT_REPORT_READ')")
  public ShiftReportLog getShiftReportLog(Long id, Long tenant) {
    Optional<ShiftReportLog> shiftReportLog =
        shiftReportLogRepository.findByIdAndTenantId(id, tenant);

    if (shiftReportLog.isEmpty()) {
      throw new ShiftReportLogNotFoundException(id);
    }

    return shiftReportLog.get();
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN')
      or @membershipService.hasPrivilege(#ancestorGroup, authentication.principal.userId, 'SHIFT_REPORT_READ')
      or @membershipService.hasPrivilege(#group, authentication.principal.userId, 'SHIFT_REPORT_READ')
      """)
  public Page<ShiftReportLog> getShiftReportLogs(
      Long tenantId,
      Long shiftReportSubTopicId,
      Long group,
      Long ancestorGroup,
      Long topic,
      Long subTopic,
      ShiftReportLogPriority priority,
      Long startDateGte,
      Long startDateLte,
      String search,
      String sort,
      Long pageNumber,
      Long pageSize) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, ShiftReportLog_.CREATION_DATE);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(
                  sort, ShiftReportLogSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    ShiftReportLogSpecificationBuilder spec =
        new ShiftReportLogSpecificationBuilder()
            .tenant(tenantId)
            .group(group)
            .ancestorGroup(ancestorGroup)
            .topic(topic)
            .subTopic(subTopic)
            .search(search)
            .shiftLogSubTopic(shiftReportSubTopicId)
            .priority(priority)
            .createDateGte(startDateGte)
            .createDateLte(startDateLte);

    return shiftReportLogRepository.findAll(spec.build(), pageable);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(@shiftReportLogService.getShiftReportSubTopic(#shiftReportLog.shiftReportSubTopic.id).shiftReport.group.id, authentication.principal.userId, 'SHIFT_REPORT_CREATE')")
  public ShiftReportLog createShiftReportLog(ShiftReportLog shiftReportLog) {
    ShiftReportLog newShiftReportLog = shiftReportLogRepository.save(shiftReportLog);
    return getShiftReportLog(newShiftReportLog.getId(), newShiftReportLog.getTenant().getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(@shiftReportLogService.getShiftReportSubTopic(#shiftReportLog.shiftReportSubTopic.id).shiftReport.group.id, authentication.principal.userId, 'SHIFT_REPORT_UPDATE')")
  public ShiftReportLog updatReportLog(ShiftReportLog updatedShiftReportLog) {
    ShiftReportLog currentShiftReportLog =
        getShiftReportLog(updatedShiftReportLog.getId(), updatedShiftReportLog.getTenant().getId());
    ShiftReport shiftReport = currentShiftReportLog.getShiftReportSubTopic().getShiftReport();

    if (Boolean.TRUE.equals(shiftReport.getLocked())) {
      throw new ShiftReportLockedException();
    }

    ShiftReportLog newShiftReportLog = shiftReportLogRepository.save(updatedShiftReportLog);
    return getShiftReportLog(newShiftReportLog.getId(), newShiftReportLog.getTenant().getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(@shiftReportLogService.getShiftReportSubTopic(#shiftReportLog.shiftReportSubTopic.id).shiftReport.group.id, authentication.principal.userId, 'SHIFT_REPORT_DELETE')")
  public Boolean checkDeletable(ShiftReportLog shiftReportLog) {
    return !shiftReportLogRepository.existsAnyRelationsById(shiftReportLog.getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(@shiftReportLogService.getShiftReportSubTopic(#shiftReportLog.shiftReportSubTopic.id).shiftReport .group.id, authentication.principal.userId, 'SHIFT_REPORT_DELETE')")
  public void deleteShiftReportLog(ShiftReportLog shiftReportLog) {
    if (!checkDeletable(shiftReportLog)) {
      throw new ShiftReportLogNotDeletableException(shiftReportLog.getId());
    }
    shiftReportLogRepository.delete(shiftReportLog);
  }
}
