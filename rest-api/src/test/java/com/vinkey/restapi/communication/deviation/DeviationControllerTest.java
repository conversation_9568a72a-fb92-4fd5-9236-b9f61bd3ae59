package com.vinkey.restapi.communication.deviation;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vinkey.restapi.ResponseBodyMatcher;
import com.vinkey.restapi.WithMockJWTDetails;
import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.communication.deviation.builder.DeviationChangeBuilder;
import com.vinkey.restapi.communication.deviation.builder.DeviationCreateBuilder;
import com.vinkey.restapi.communication.deviation.builder.DeviationMother;
import com.vinkey.restapi.communication.deviation.builder.DeviationReadBuilder;
import com.vinkey.restapi.communication.deviation.builder.DeviationUpdateBuilder;
import com.vinkey.restapi.communication.deviation.dto.DeviationChange;
import com.vinkey.restapi.communication.deviation.dto.DeviationCreate;
import com.vinkey.restapi.communication.deviation.dto.DeviationRead;
import com.vinkey.restapi.communication.deviation.dto.DeviationUpdate;
import com.vinkey.restapi.identityandaccess.auth.access.AccessTokenService;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.identityandaccess.auth.jwt.JwtIssuerService;
import com.vinkey.restapi.identityandaccess.identity.IdentityDetailService;
import com.vinkey.restapi.location.builder.LocationDisplayBuilder;
import com.vinkey.restapi.location.builder.LocationMother;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

@WebMvcTest(DeviationController.class)
public class DeviationControllerTest {
  @MockBean private DeviationService deviationService;
  @MockBean private DeviationHistoryService deviationHistoryService;
  @MockBean private JwtIssuerService jwtIssuerService;
  @MockBean private IdentityDetailService identityDetailsService;
  @MockBean private AccessTokenService accessTokenService;
  @MockBean private FileService fileService;
  private DeviationMapper deviationMapper = DeviationMapper.INSTANCE;

  @Autowired private MockMvc mockMvc;
  private static ObjectMapper mapper = new ObjectMapper();

  public JwtDetails getMockJwtDetails() {
    JwtDetails jwtDetails =
        (JwtDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    return jwtDetails;
  }

  @Test
  @WithMockJWTDetails
  public void createDeviation_withDeviationCreate_returnsIsCreated() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    DeviationCreate deviationCreate = DeviationCreateBuilder.aValidCreate().build();
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    DeviationRead deviationRead =
        DeviationReadBuilder.aValidRead()
            .withProcessInstanceId(deviation.getProcessInstance().getId())
            .build();
    String json = mapper.writeValueAsString(deviationCreate);

    given(deviationService.create(any(Deviation.class))).willReturn(1L);
    given(deviationService.read(1L, jwtDetails.getTenantId())).willReturn(deviation);

    ResultActions result =
        mockMvc.perform(
            post("/v1/deviations").contentType(MediaType.APPLICATION_JSON).content(json));

    result
        .andExpect(status().isCreated())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(deviationRead));
  }

  @Test
  @WithMockJWTDetails
  public void getDeviation_withId_returnsOkAndDeviation() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    DeviationRead deviationRead =
        DeviationReadBuilder.aValidRead()
            .withProcessInstanceId(deviation.getProcessInstance().getId())
            .build();

    given(deviationService.read(1L, jwtDetails.getTenantId())).willReturn(deviation);

    ResultActions result = mockMvc.perform(get("/v1/deviations/{id}", 1L));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(deviationRead));
  }

  @Test
  @WithMockJWTDetails
  public void getDeviations_withPageNumbersNoFilter_returnsOkAndPage() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Long groupId = 2L;
    Long ancestorGroupId = 11L;
    Long pathGroupId = 12L;
    DeviationStatus status = DeviationStatus.OPEN;
    DeviationStatus statusNot = DeviationStatus.CLOSED;
    Long createdBy = 10L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    String filter = "";
    String search = "";
    Long pageNumber = 0L;
    Long pageSize = 20L;

    Deviation deviation = DeviationMother.aSavedDeviation().build();
    Page<Deviation> deviationPage = new PageImpl<>(List.of(deviation));

    given(
            deviationService.readPage(
                jwtDetails.getTenantId(),
                groupId,
                ancestorGroupId,
                pathGroupId,
                status,
                statusNot,
                createdBy,
                candidateGroups,
                filter,
                search,
                null,
                pageNumber,
                pageSize,
                null,
                null))
        .willReturn(deviationPage);

    ResultActions result =
        mockMvc.perform(
            get("/v1/deviations")
                .param("groupId", groupId.toString())
                .param("ancestorGroupId", ancestorGroupId.toString())
                .param("pathGroupId", pathGroupId.toString())
                .param("status", status.toString())
                .param("statusNot", statusNot.toString())
                .param("createdBy", createdBy.toString())
                .param("search", search)
                .param("candidateGroups", String.join(", ", candidateGroups))
                .param("filter", filter)
                .param("pageNumber", pageNumber.toString())
                .param("pageSize", pageSize.toString())
                .param("locationId", (String) null)
                .param("ancestorLocationId", (String) null));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(
                    deviationMapper.paginatedDeviationsToPaginatedDeviationReads(deviationPage)));
  }

  @Test
  @WithMockJWTDetails
  public void getDeviations_withFilterNoPageArgs_returnsOkAndPage() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Long ancestorGroupId = 11L;
    Long pathGroupId = 12L;
    DeviationStatus status = DeviationStatus.OPEN;
    DeviationStatus statusNot = DeviationStatus.CLOSED;
    Long createdBy = 10L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    String filter = "groupId=2";
    String search = "";

    Deviation deviation = DeviationMother.aSavedDeviation().build();
    Page<Deviation> deviationPage = new PageImpl<>(List.of(deviation));

    given(
            deviationService.readPage(
                jwtDetails.getTenantId(),
                null,
                ancestorGroupId,
                pathGroupId,
                status,
                statusNot,
                createdBy,
                candidateGroups,
                filter,
                search,
                null,
                null,
                null,
                null,
                null))
        .willReturn(deviationPage);

    ResultActions result =
        mockMvc.perform(
            get("/v1/deviations")
                .param("ancestorGroupId", ancestorGroupId.toString())
                .param("pathGroupId", pathGroupId.toString())
                .param("status", status.toString())
                .param("statusNot", statusNot.toString())
                .param("createdBy", createdBy.toString())
                .param("search", search)
                .param("candidateGroups", String.join(", ", candidateGroups))
                .param("filter", filter)
                .param("locationId", (String) null)
                .param("ancestorLocationId", (String) null));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(
                    deviationMapper.paginatedDeviationsToPaginatedDeviationReads(deviationPage)));
  }

  @Test
  @WithMockJWTDetails
  public void updateDeviation_withDeviationUdate_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    DeviationUpdate deviationUpdate = DeviationUpdateBuilder.aValidUpdate().build();
    Deviation updatedDeviation =
        DeviationMother.aSavedDeviation()
            .withLocation(LocationMother.aSavedLocation().withId(9L).build())
            .withDescription(deviationUpdate.getDescription())
            .build();
    String json = mapper.writeValueAsString(deviationUpdate);
    DeviationRead deviationRead =
        DeviationReadBuilder.aValidRead()
            .withLocation(
                LocationDisplayBuilder.aValidLocationDisplay()
                    .withId(deviationUpdate.getLocation())
                    .build())
            .withDescription(deviationUpdate.getDescription())
            .withProcessInstanceId(deviation.getProcessInstance().getId())
            .build();

    given(deviationService.read(deviation.getId(), jwtDetails.getTenantId())).willReturn(deviation);
    given(deviationService.update(any(Deviation.class))).willReturn(updatedDeviation);

    ResultActions result =
        mockMvc.perform(
            put("/v1/deviations/{id}", deviation.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(json));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(deviationRead));
  }

  @Test
  @WithMockJWTDetails
  public void checkDeletable_withDeviation_returnsOkAndTrue() throws Exception {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    JwtDetails jwtDetails = getMockJwtDetails();
    Deletable deletable = new Deletable();
    deletable.setDeletable(true);

    given(deviationService.read(deviation.getId(), jwtDetails.getTenantId())).willReturn(deviation);
    given(deviationService.checkDeletable(deviation)).willReturn(true);

    ResultActions result = mockMvc.perform(get("/v1/deviations/{id}/deletable", deviation.getId()));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(deletable));
  }

  @Test
  @WithMockJWTDetails
  public void deleteDeviation_withDeviation_returnsNoContent() throws Exception {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    JwtDetails jwtDetails = getMockJwtDetails();

    given(deviationService.read(deviation.getId(), jwtDetails.getTenantId())).willReturn(deviation);

    ResultActions result = mockMvc.perform(delete("/v1/deviations/{id}", deviation.getId()));

    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void cancelDeviation_withDeviation_returnsNoContent() throws Exception {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    JwtDetails jwtDetails = getMockJwtDetails();

    given(deviationService.read(deviation.getId(), jwtDetails.getTenantId())).willReturn(deviation);

    ResultActions result = mockMvc.perform(post("/v1/deviations/{id}/cancel", deviation.getId()));

    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void getHistory_withId_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    DeviationChange deviationChange = DeviationChangeBuilder.aValidChange().build();
    List<DeviationChange> list = List.of(deviationChange);

    given(deviationHistoryService.getHistory(1L, jwtDetails.getTenantId())).willReturn(list);

    ResultActions result = mockMvc.perform(get("/v1/deviations/{id}/history", 1L));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(list));
  }

  @Test
  @WithMockJWTDetails
  public void generateDeviationPdf_withIdAndTimezone_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationService.read(deviation.getId(), jwtDetails.getTenantId())).willReturn(deviation);

    ResultActions result = mockMvc.perform(get("/v1/deviations/{id}/pdf", deviation.getId()));

    result.andExpect(status().isOk());
  }
}
