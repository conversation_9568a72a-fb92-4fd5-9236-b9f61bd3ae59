package com.vinkey.restapi.communication.deviation;

import static org.assertj.core.api.BDDAssertions.then;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.vinkey.restapi.communication.deviation.builder.DeviationMother;
import com.vinkey.restapi.communication.deviation.exception.DeviationLockedException;
import com.vinkey.restapi.communication.deviation.exception.DeviationNotDeletableException;
import com.vinkey.restapi.communication.deviation.exception.DeviationNotFoundException;
import com.vinkey.restapi.communication.deviation.notification.event.DeviationCanceledEvent;
import com.vinkey.restapi.communication.deviation.notification.event.DeviationClosedEvent;
import com.vinkey.restapi.communication.deviation.notification.event.DeviationUpdatedEvent;
import com.vinkey.restapi.flowable.process.builder.ProcessInstanceCustomMother;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Optional;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.context.IContext;

@ExtendWith(MockitoExtension.class)
public class DeviationServiceTest {
  @InjectMocks private DeviationService deviationService;
  @Mock private DeviationRepository deviationRepository;
  @Mock private ApplicationEventPublisher applicationEventPublisher;
  @Mock private RuntimeService runtimeService;
  @Mock private ITemplateEngine templateEngine;

  @Test
  public void create_withDeviation_returnsDeviationId() {
    ProcessInstanceBuilder instanceBuilder = mock(ProcessInstanceBuilder.class);
    ProcessInstance processInstance = mock(ProcessInstance.class);
    Deviation deviation =
        DeviationMother.aSavedDeviation()
            .but()
            .withId(771L)
            .withProcessInstance(
                ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build())
            .build();

    given(runtimeService.createProcessInstanceBuilder()).willReturn(instanceBuilder);
    given(instanceBuilder.start()).willReturn(processInstance);
    given(deviationRepository.save(deviation)).willReturn(deviation);

    Long result = deviationService.create(deviation);

    verify(runtimeService).setVariables(eq(deviation.getProcessInstance().getId()), anyMap());
    verify(instanceBuilder).start();
    then(result).isEqualTo(771L);
  }

  @Test
  public void read_withIdAndTenantId_returnsDeviation() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationRepository.findByIdAndTenantId(deviation.getId(), deviation.getTenant().getId()))
        .willReturn(Optional.of(deviation));

    Deviation result = deviationService.read(deviation.getId(), deviation.getTenant().getId());

    then(result).isNotNull();
    then(result.getId()).isEqualTo(deviation.getId());
    then(result.getSid()).isEqualTo(deviation.getSid());
    then(result.getTenant().getId()).isEqualTo(deviation.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(deviation.getGroup().getId());
    then(result.getLocation().getId()).isEqualTo(deviation.getLocation().getId());
    then(result.getDescription()).isEqualTo(deviation.getDescription());
    then(result.getLocked()).isEqualTo(deviation.getLocked());
    then(result.getStatus()).isEqualTo(deviation.getStatus());
    then(result.getCreationDate()).isEqualTo(deviation.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(deviation.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(deviation.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(deviation.getModifiedBy().getId());
  }

  @Test
  public void read_withWrongId_returnsNotFoundException() {
    given(deviationRepository.findByIdAndTenantId(1L, 2L)).willReturn(Optional.empty());

    DeviationNotFoundException error =
        assertThrows(DeviationNotFoundException.class, () -> deviationService.read(1L, 2L));

    then(error.getMessage()).isEqualTo("Deviation not found with id: 1");
  }

  @Test
  public void update_withDeviation_returnsDeviationAndVerifyNotification() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationRepository.save(deviation)).willReturn(deviation);
    given(deviationRepository.findByIdAndTenantId(deviation.getId(), deviation.getTenant().getId()))
        .willReturn(Optional.of(deviation));

    Deviation result = deviationService.update(deviation);

    then(result).isNotNull();
    then(result.getId()).isEqualTo(deviation.getId());
    then(result.getSid()).isEqualTo(deviation.getSid());
    then(result.getTenant().getId()).isEqualTo(deviation.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(deviation.getGroup().getId());
    then(result.getLocation().getId()).isEqualTo(deviation.getLocation().getId());
    then(result.getDescription()).isEqualTo(deviation.getDescription());
    then(result.getLocked()).isEqualTo(deviation.getLocked());
    then(result.getStatus()).isEqualTo(deviation.getStatus());
    then(result.getCreationDate()).isEqualTo(deviation.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(deviation.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(deviation.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(deviation.getModifiedBy().getId());
    verify(applicationEventPublisher).publishEvent(new DeviationUpdatedEvent(deviation.getId()));
  }

  @Test
  public void update_withLockedDeviation_throwsLockedException() {
    Deviation deviation = DeviationMother.aSavedDeviation().but().withLocked(true).build();
    Long id = deviation.getId();

    DeviationLockedException error =
        assertThrows(DeviationLockedException.class, () -> deviationService.update(deviation));

    then(error.getMessage()).isEqualTo("Deviation with id " + id + " is locked");
  }

  @Test
  public void generatePdfContext_withDevation_returnsContext() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    String timeZone = "UTC";

    Context context = deviationService.generatePdfContext(deviation, timeZone);

    then(context).isNotNull();
    then(context.getVariable("deviation")).isEqualTo(deviation);
    then(context.getVariable("timeZone")).isEqualTo(timeZone);
  }

  @Test
  public void generatePdf_withArgs_generatesPdf() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    given(templateEngine.process(eq("pdf/deviation.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

    deviationService.generatePdf(deviation, "UTC", outputStream);

    then(outputStream.size()).isGreaterThan(0);
  }

  @Test
  public void checkDeletable_withDeletableDeviation_returnsTrue() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationRepository.existsAnyRelationsById(deviation.getId())).willReturn(false);

    Boolean result = deviationService.checkDeletable(deviation);

    then(result).isTrue();
  }

  @Test
  public void checkDeletable_withNotDeletableDeviation_returnsFalse() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationRepository.existsAnyRelationsById(deviation.getId())).willReturn(true);

    Boolean result = deviationService.checkDeletable(deviation);

    then(result).isFalse();
  }

  @Test
  public void deleteDeviation_withDeletableDeviation_deletes() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationRepository.existsAnyRelationsById(deviation.getId())).willReturn(false);

    deviationService.delete(deviation);

    verify(deviationRepository).delete(deviation);
  }

  @Test
  public void deleteDeviation_withNonDeletableDeviation_throwsNotDeletableException() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    Long id = deviation.getId();

    given(deviationRepository.existsAnyRelationsById(deviation.getId())).willReturn(true);

    DeviationNotDeletableException error =
        assertThrows(
            DeviationNotDeletableException.class, () -> deviationService.delete(deviation));

    then(error.getMessage()).isEqualTo("Deviation with id " + id + " is not deletable");
  }

  @Test
  public void cancelDeviation_withNoProcessInstance_verifyChangesAndNotification() {
    Deviation deviation = DeviationMother.aSavedDeviation().but().withProcessInstance(null).build();

    deviationService.cancelDeviation(deviation);

    verify(deviationRepository).save(deviation);
    verify(applicationEventPublisher).publishEvent(new DeviationCanceledEvent(deviation.getId()));
    then(deviation.getStatus()).isEqualTo(DeviationStatus.CANCELED);
    then(deviation.getLocked()).isTrue();
  }

  @Test
  public void cancelDeviation_withProcessInstance_verifyChangesAndNotification() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    deviation.getProcessInstance().setId("9");

    deviationService.cancelDeviation(deviation);

    verify(deviationRepository).save(deviation);
    verify(runtimeService)
        .deleteProcessInstance(
            deviation.getProcessInstance().getId(), "Deviation canceled by user");
    verify(applicationEventPublisher).publishEvent(new DeviationCanceledEvent(deviation.getId()));
    then(deviation.getStatus()).isEqualTo(DeviationStatus.CANCELED);
    then(deviation.getLocked()).isTrue();
  }

  @Test
  public void lock_withIds_locksDeviation() {
    Deviation deviation = DeviationMother.aSavedDeviation().build();

    given(deviationRepository.findByIdAndTenantId(deviation.getId(), deviation.getTenant().getId()))
        .willReturn(Optional.of(deviation));

    deviationService.lock(deviation.getId(), deviation.getTenant().getId());

    verify(deviationRepository).save(deviation);
    then(deviation.getLocked()).isTrue();
  }

  @Test
  public void close_withIdsAndReason_closesDeviation() {
    String closeReason = "Testing reasons";
    Deviation deviation = DeviationMother.aSavedDeviation().build();
    given(deviationRepository.findByIdAndTenantId(deviation.getId(), deviation.getTenant().getId()))
        .willReturn(Optional.of(deviation));

    deviationService.close(deviation.getId(), deviation.getTenant().getId(), closeReason);

    then(deviation.getStatus()).isEqualTo(DeviationStatus.CLOSED);
    then(deviation.getCloseReason()).isEqualTo(closeReason);
    verify(deviationRepository).save(deviation);
    verify(applicationEventPublisher).publishEvent(new DeviationClosedEvent(deviation.getId()));
  }

  @Test
  public void readPage_withPageNumbers_returnsPage() {
    Long tenantId = 1L;
    Long groupId = 2L;
    Long ancestorGroupId = 11L;
    Long pathGroupId = 12L;
    DeviationStatus status = DeviationStatus.OPEN;
    DeviationStatus statusNot = DeviationStatus.CLOSED;
    Long createdBy = 10L;
    List<String> candidateGroups = null;
    String filter = null;
    String search = "deviation";
    Long pageNumber = 0L;
    Long pageSize = 20L;

    Deviation deviation = DeviationMother.aSavedDeviation().build();
    Page<Deviation> deviationPage = new PageImpl<>(List.of(deviation));

    given(deviationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .willReturn(deviationPage);

    Page<Deviation> resultPage =
        deviationService.readPage(
            tenantId,
            groupId,
            ancestorGroupId,
            pathGroupId,
            status,
            statusNot,
            createdBy,
            candidateGroups,
            filter,
            search,
            null,
            pageNumber,
            pageSize,
            null,
            null);

    Deviation result = resultPage.getContent().get(0);

    then(resultPage.getTotalElements()).isEqualTo(1);
    then(result.getId()).isEqualTo(deviation.getId());
    then(result.getSid()).isEqualTo(deviation.getSid());
    then(result.getTenant().getId()).isEqualTo(deviation.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(deviation.getGroup().getId());
    then(result.getLocation().getId()).isEqualTo(deviation.getLocation().getId());
    then(result.getDescription()).isEqualTo(deviation.getDescription());
    then(result.getLocked()).isEqualTo(deviation.getLocked());
    then(result.getStatus()).isEqualTo(deviation.getStatus());
    then(result.getCreationDate()).isEqualTo(deviation.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(deviation.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(deviation.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(deviation.getModifiedBy().getId());
  }

  @Test
  public void readPage_withFilterNoPageNums_returnsPage() {
    Long tenantId = null;
    Long groupId = 2L;
    Long ancestorGroupId = 11L;
    Long pathGroupId = 12L;
    DeviationStatus status = DeviationStatus.OPEN;
    DeviationStatus statusNot = DeviationStatus.CLOSED;
    Long createdBy = 10L;
    List<String> candidateGroups = null;
    String filter = "tenantId=1";
    String search = "deviation";
    Long pageNumber = null;
    Long pageSize = null;

    Deviation deviation = DeviationMother.aSavedDeviation().build();
    Page<Deviation> deviationPage = new PageImpl<>(List.of(deviation));

    given(deviationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .willReturn(deviationPage);

    Page<Deviation> resultPage =
        deviationService.readPage(
            tenantId,
            groupId,
            ancestorGroupId,
            pathGroupId,
            status,
            statusNot,
            createdBy,
            candidateGroups,
            filter,
            search,
            null,
            pageNumber,
            pageSize,
            null,
            null);

    Deviation result = resultPage.getContent().get(0);

    then(resultPage.getTotalElements()).isEqualTo(1);
    then(result.getId()).isEqualTo(deviation.getId());
    then(result.getSid()).isEqualTo(deviation.getSid());
    then(result.getTenant().getId()).isEqualTo(deviation.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(deviation.getGroup().getId());
    then(result.getLocation().getId()).isEqualTo(deviation.getLocation().getId());
    then(result.getDescription()).isEqualTo(deviation.getDescription());
    then(result.getLocked()).isEqualTo(deviation.getLocked());
    then(result.getStatus()).isEqualTo(deviation.getStatus());
    then(result.getCreationDate()).isEqualTo(deviation.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(deviation.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(deviation.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(deviation.getModifiedBy().getId());
  }
}
