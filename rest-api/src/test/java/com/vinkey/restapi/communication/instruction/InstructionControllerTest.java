package com.vinkey.restapi.communication.instruction;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vinkey.restapi.ResponseBodyMatcher;
import com.vinkey.restapi.WithMockJWTDetails;
import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.communication.instruction.builder.InstructionChangeBuilder;
import com.vinkey.restapi.communication.instruction.builder.InstructionCreateBuilder;
import com.vinkey.restapi.communication.instruction.builder.InstructionMother;
import com.vinkey.restapi.communication.instruction.builder.InstructionUpdateBuilder;
import com.vinkey.restapi.communication.instruction.dto.InstructionChange;
import com.vinkey.restapi.communication.instruction.dto.InstructionCreate;
import com.vinkey.restapi.communication.instruction.dto.InstructionUpdate;
import com.vinkey.restapi.identityandaccess.auth.access.AccessTokenService;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.identityandaccess.auth.jwt.JwtIssuerService;
import com.vinkey.restapi.identityandaccess.identity.IdentityDetailService;
import com.vinkey.restapi.identityandaccess.tenant.builder.TenantMother;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

@WebMvcTest(InstructionController.class)
public class InstructionControllerTest {
  @MockBean private JwtIssuerService jwtIssuerService;
  @MockBean private IdentityDetailService identityDetailsService;

  @MockBean private AccessTokenService accessTokenService;

  private static ObjectMapper mapper = new ObjectMapper();

  @Autowired private MockMvc mockMvc;

  @MockBean private InstructionService instructionService;

  private InstructionMapper instructionMapper = InstructionMapper.INSTANCE;

  @MockBean private InstructionHistoryService instructionHistoryService;

  @MockBean private FileService fileService;

  public JwtDetails getMockJwtDetails() {
    JwtDetails jwtDetails =
        (JwtDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    return jwtDetails;
  }

  @Test
  @WithMockJWTDetails
  public void createInstruction_withInstrucitonCreate_returnsCreatedAndInstruction()
      throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    InstructionCreate instructionCreate =
        InstructionCreateBuilder.anEmptyCreate()
            .withDescription(instruction.getDescription())
            .withEndTime(instruction.getEndTime())
            .withStartTime(instruction.getStartTime())
            .withGroup(instruction.getGroup().getId())
            .build();

    String json = mapper.writeValueAsString(instructionCreate);

    given(instructionService.create(any(Instruction.class))).willReturn(instruction.getId());
    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    ResultActions result =
        mockMvc.perform(
            post("/v1/instructions").contentType(MediaType.APPLICATION_JSON).content(json));

    result
        .andExpect(status().isCreated())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(instructionMapper.instructionToInstructionRead(instruction)));
  }

  @Test
  @WithMockJWTDetails
  public void getInstruction_withIdAndTenantId_returnsOkAndInstruction() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    ResultActions result = mockMvc.perform(get("/v1/instructions/{id}", instruction.getId()));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(instructionMapper.instructionToInstructionRead(instruction)));
  }

  @Test
  @WithMockJWTDetails
  public void getInstructions_withArgs_returnsOkAndPage() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Instruction instruction =
        InstructionMother.aSavedInstruction()
            .withTenant(TenantMother.aSavedTenant().but().withId(jwtDetails.getTenantId()).build())
            .build();
    List<Instruction> list = List.of(instruction);

    Long tenantId = instruction.getTenant().getId();
    Long groupId = instruction.getGroup().getId();
    Long ancestorGroupId = 6L;
    Long pathGroupId = 8L;
    Long startDateLte = instruction.getStartTime();
    Long startDateGte = instruction.getStartTime() + 1L;
    Long endDateLte = instruction.getEndTime();
    Long endDateGte = instruction.getEndTime() + 1L;
    Boolean endDateNotNull = false;
    Boolean endDateNull = false;
    InstructionStatus status = instruction.getStatus();
    InstructionStatus statusNot = InstructionStatus.CLOSED;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long createdBy = instruction.getCreatedBy().getId();
    String filter = "";
    String search = "";
    Long pageNumber = 0L;
    Long pageSize = 20L;

    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(Sort.Direction.DESC, "startDate"));

    Page<Instruction> page = new PageImpl<>(list, pageable, list.size());

    given(
            instructionService.readPage(
                tenantId,
                groupId,
                ancestorGroupId,
                pathGroupId,
                startDateLte,
                startDateGte,
                endDateLte,
                endDateGte,
                endDateNotNull,
                endDateNull,
                status,
                statusNot,
                candidateGroups,
                createdBy,
                filter,
                search,
                null,
                pageNumber,
                pageSize))
        .willReturn(page);

    ResultActions result =
        mockMvc.perform(
            get("/v1/instructions")
                .param("tenantId", tenantId.toString())
                .param("groupId", groupId.toString())
                .param("ancestorGroupId", ancestorGroupId.toString())
                .param("pathGroupId", pathGroupId.toString())
                .param("startDateLte", startDateLte.toString())
                .param("startDateGte", startDateGte.toString())
                .param("endDateLte", endDateLte.toString())
                .param("endDateGte", endDateGte.toString())
                .param("endDateNull", String.valueOf(endDateNull))
                .param("endDateNotNull", String.valueOf(endDateNotNull))
                .param("status", status.toString())
                .param("statusNot", statusNot.toString())
                .param("createdBy", createdBy.toString())
                .param("candidateGroups", String.join(", ", candidateGroups))
                .param("search", search)
                .param("pageNumber", pageNumber.toString())
                .param("pageSize", pageSize.toString())
                .param("filter", filter));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(
                    instructionMapper.paginatedInstructionsToPaginatedInstructionReads(page)));
  }

  @Test
  @WithMockJWTDetails
  public void updateInstruction_withInstructionUpdate_returnsInstruction() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    InstructionUpdate instructionUpdate = InstructionUpdateBuilder.aValidUpdate().build();

    String json = mapper.writeValueAsString(instructionUpdate);

    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    given(instructionService.update(instruction)).willReturn(instruction);

    ResultActions result =
        mockMvc.perform(
            put("/v1/instructions/{id}", instruction.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(json));

    instructionMapper.updateInstructionFromInstructionUpdate(instruction, instructionUpdate);

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(instructionMapper.instructionToInstructionRead(instruction)));
  }

  @Test
  @WithMockJWTDetails
  public void checkDeletable_withInstruction_returnsOkAndTrue() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    Deletable deletable = new Deletable();
    deletable.setDeletable(true);

    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    given(instructionService.checkDeletable(instruction)).willReturn(true);

    ResultActions result =
        mockMvc.perform(get("/v1/instructions/{id}/deletable", instruction.getId()));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(deletable));
  }

  @Test
  @WithMockJWTDetails
  public void deleteInstruction_withInstruction_returnsNoContent() throws Exception {
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    JwtDetails jwtDetails = getMockJwtDetails();

    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    ResultActions result = mockMvc.perform(delete("/v1/instructions/{id}", instruction.getId()));

    verify(instructionService).delete(instruction);
    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void cancelInstruction_withInstruction_returnsOk() throws Exception {
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    JwtDetails jwtDetails = getMockJwtDetails();

    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    ResultActions result =
        mockMvc.perform(post("/v1/instructions/{id}/cancel", instruction.getId()));

    verify(instructionService).cancel(instruction);
    result.andExpect(status().isOk());
  }

  @Test
  @WithMockJWTDetails
  public void getHistory_withId_returnsOkAndHistory() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    InstructionChange instrucitonChange = InstructionChangeBuilder.aValidChange().build();
    List<InstructionChange> list = List.of(instrucitonChange);

    given(instructionHistoryService.getHistory(1L, jwtDetails.getTenantId())).willReturn(list);

    ResultActions result = mockMvc.perform(get("/v1/instructions/{id}/history", 1L));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(list));
  }

  @Test
  @WithMockJWTDetails
  public void generateInstructionPdf_withId_returnsOk() throws Exception {
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    String timeZone = "UTC";
    JwtDetails jwtDetails = getMockJwtDetails();

    given(instructionService.read(instruction.getId(), jwtDetails.getTenantId()))
        .willReturn(instruction);

    ResultActions result =
        mockMvc.perform(
            get("/v1/instructions/{id}/pdf", instruction.getId()).param("timeZone", timeZone));

    verify(instructionService).generatePdf(eq(instruction), eq(timeZone), any());
    result.andExpect(status().isOk());
  }
}
