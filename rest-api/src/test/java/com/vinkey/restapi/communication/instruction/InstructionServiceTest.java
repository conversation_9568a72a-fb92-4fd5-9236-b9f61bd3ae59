package com.vinkey.restapi.communication.instruction;

import static org.assertj.core.api.BDDAssertions.then;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.communication.instruction.builder.InstructionMother;
import com.vinkey.restapi.communication.instruction.exception.InstructionLockedException;
import com.vinkey.restapi.communication.instruction.exception.InstructionNotFoundException;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionActivatedEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionCanceledEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionClosedEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionPlannedEvent;
import com.vinkey.restapi.communication.instruction.notification.event.InstructionUpdatedEvent;
import com.vinkey.restapi.flowable.process.builder.ProcessInstanceCustomMother;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ExecutionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.context.IContext;

@ExtendWith(MockitoExtension.class)
public class InstructionServiceTest {
  @InjectMocks private InstructionService instructionService;
  @Mock private InstructionRepository instructionRepository;
  @Mock private RuntimeService runtimeService;
  @Mock private ApplicationEventPublisher applicationEventPublisher;
  @Mock private ITemplateEngine templateEngine;

  @Test
  public void read_withCorrectId_returnsInstruction() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    Instruction result =
        instructionService.read(instruction.getId(), instruction.getTenant().getId());

    then(result.getId()).isEqualTo(instruction.getId());
    then(result.getSid()).isEqualTo(instruction.getSid());
    then(result.getDescription()).isEqualTo(instruction.getDescription());
    then(result.getLocked()).isFalse();
    then(result.getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getStartTime()).isEqualTo(instruction.getStartTime());
    then(result.getEndTime()).isEqualTo(instruction.getEndTime());
    then(result.getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(instruction.getGroup().getId());
    then(result.getCreationDate()).isEqualTo(instruction.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(instruction.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(instruction.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(instruction.getModifiedBy().getId());
  }

  @Test
  public void read_withWrongId_returnsInstruction() {
    given(instructionRepository.findByIdAndTenantId(1L, 2L)).willReturn(Optional.empty());

    InstructionNotFoundException exception =
        assertThrows(InstructionNotFoundException.class, () -> instructionService.read(1L, 2L));

    then(exception.getMessage()).isEqualTo("Instruction not found with id: 1");
  }

  @Test
  public void retrieveFilterKeyValue_withKeyAndFilter_returnsLong() {
    SpecFilterCriteria criteria1 = mock(SpecFilterCriteria.class);
    SpecFilterCriteria criteria2 = mock(SpecFilterCriteria.class);

    List<SpecFilterCriteria> decodedFilter = Arrays.asList(criteria1, criteria2);

    given(criteria1.getKey()).willReturn("key1");
    given(criteria1.getValue()).willReturn("123");
    given(criteria2.getKey()).willReturn("key1");
    given(criteria2.getValue()).willReturn("456");

    Long result = instructionService.retrieveFilterKeyValue(decodedFilter, "key1");

    then(result).isEqualTo(123L);
  }

  @Test
  public void retrieveFilterKeyValue_withourFilter_returnsNull() {
    Long result = instructionService.retrieveFilterKeyValue(null, "key1");

    then(result).isNull();
    ;
  }

  @Test
  public void retrieveFilterKeyValue_withWrongKeyAndFilter_returnsNull() {
    SpecFilterCriteria criteria1 = mock(SpecFilterCriteria.class);
    SpecFilterCriteria criteria2 = mock(SpecFilterCriteria.class);

    List<SpecFilterCriteria> decodedFilter = Arrays.asList(criteria1, criteria2);

    given(criteria1.getKey()).willReturn("key1");
    given(criteria2.getKey()).willReturn("key1");

    Long result = instructionService.retrieveFilterKeyValue(decodedFilter, "key2");

    then(result).isNull();
  }

  @Test
  public void readPage_withValidCreds_returnsPage() {

    Instruction instruction = InstructionMother.aSavedInstruction().build();
    List<Instruction> list = List.of(instruction);

    Long tenantId = instruction.getTenant().getId();
    Long groupId = instruction.getGroup().getId();
    Long ancestorGroupId = 6L;
    Long pathGroupId = 8L;
    Long startTimeLte = instruction.getStartTime();
    Long startTimeGte = instruction.getStartTime() + 1L;
    Long endTimeLte = instruction.getEndTime();
    Long endTimeGte = instruction.getEndTime() + 1L;
    Boolean endTimeNotNull = false;
    Boolean endTimeNull = false;
    InstructionStatus status = instruction.getStatus();
    InstructionStatus statusNot = InstructionStatus.CLOSED;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long createdBy = instruction.getCreatedBy().getId();
    String filter = null;
    String search = "instrction one";
    Long pageNumber = 0L;
    Long pageSize = 20L;

    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(Sort.Direction.DESC, "startTime"));

    Page<Instruction> page = new PageImpl<>(list, pageable, list.size());

    given(instructionRepository.findAll(any(Specification.class), eq(pageable))).willReturn(page);

    Page<Instruction> result =
        instructionService.readPage(
            tenantId,
            groupId,
            ancestorGroupId,
            pathGroupId,
            startTimeLte,
            startTimeGte,
            endTimeLte,
            endTimeGte,
            endTimeNotNull,
            endTimeNull,
            status,
            statusNot,
            candidateGroups,
            createdBy,
            filter,
            search,
            null,
            pageNumber,
            pageSize);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    then(result.getContent().get(0).getId()).isEqualTo(instruction.getId());
    then(result.getContent().get(0).getSid()).isEqualTo(instruction.getSid());
    then(result.getContent().get(0).getDescription()).isEqualTo(instruction.getDescription());
    then(result.getContent().get(0).getLocked()).isFalse();
    then(result.getContent().get(0).getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getContent().get(0).getStartTime()).isEqualTo(instruction.getStartTime());
    then(result.getContent().get(0).getEndTime()).isEqualTo(instruction.getEndTime());
    then(result.getContent().get(0).getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getContent().get(0).getGroup().getId()).isEqualTo(instruction.getGroup().getId());
    then(result.getContent().get(0).getCreationDate()).isEqualTo(instruction.getCreationDate());
    then(result.getContent().get(0).getModifiedDate()).isEqualTo(instruction.getModifiedDate());
    then(result.getContent().get(0).getCreatedBy().getId())
        .isEqualTo(instruction.getCreatedBy().getId());
    then(result.getContent().get(0).getModifiedBy().getId())
        .isEqualTo(instruction.getModifiedBy().getId());
  }

  @Test
  public void readPage_withNullableArgsAsNull_returnsPage() {

    Instruction instruction = InstructionMother.aSavedInstruction().build();
    List<Instruction> list = List.of(instruction);

    Long tenantId = instruction.getTenant().getId();
    Long groupId = instruction.getGroup().getId();
    Long ancestorGroupId = null;
    Long pathGroupId = 8L;
    Long startTimeLte = instruction.getStartTime();
    Long startTimeGte = instruction.getStartTime() + 1L;
    Long endTimeLte = instruction.getEndTime();
    Long endTimeGte = instruction.getEndTime() + 1L;
    Boolean endTimeNotNull = null;
    Boolean endTimeNull = null;
    InstructionStatus status = instruction.getStatus();
    InstructionStatus statusNot = InstructionStatus.CLOSED;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long createdBy = instruction.getCreatedBy().getId();
    String filter = "ancestorGroupId=6";
    String search = "instrction one";
    Long pageNumber = null;
    Long pageSize = null;

    Pageable pageable = PageRequest.of(0, 20, Sort.by(Sort.Direction.DESC, "startTime"));

    Page<Instruction> page = new PageImpl<>(list, pageable, list.size());

    given(instructionRepository.findAll(any(Specification.class), eq(pageable))).willReturn(page);

    Page<Instruction> result =
        instructionService.readPage(
            tenantId,
            groupId,
            ancestorGroupId,
            pathGroupId,
            startTimeLte,
            startTimeGte,
            endTimeLte,
            endTimeGte,
            endTimeNotNull,
            endTimeNull,
            status,
            statusNot,
            candidateGroups,
            createdBy,
            filter,
            search,
            null,
            pageNumber,
            pageSize);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    then(result.getContent().get(0).getId()).isEqualTo(instruction.getId());
    then(result.getContent().get(0).getSid()).isEqualTo(instruction.getSid());
    then(result.getContent().get(0).getDescription()).isEqualTo(instruction.getDescription());
    then(result.getContent().get(0).getLocked()).isFalse();
    then(result.getContent().get(0).getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getContent().get(0).getStartTime()).isEqualTo(instruction.getStartTime());
    then(result.getContent().get(0).getEndTime()).isEqualTo(instruction.getEndTime());
    then(result.getContent().get(0).getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getContent().get(0).getGroup().getId()).isEqualTo(instruction.getGroup().getId());
    then(result.getContent().get(0).getCreationDate()).isEqualTo(instruction.getCreationDate());
    then(result.getContent().get(0).getModifiedDate()).isEqualTo(instruction.getModifiedDate());
    then(result.getContent().get(0).getCreatedBy().getId())
        .isEqualTo(instruction.getCreatedBy().getId());
    then(result.getContent().get(0).getModifiedBy().getId())
        .isEqualTo(instruction.getModifiedBy().getId());
  }

  @Test
  public void readPage_withEndTimeAndTimeNotNullAsTrue_returnPage() {

    Instruction instruction = InstructionMother.aSavedInstruction().build();
    List<Instruction> list = List.of(instruction);

    Long tenantId = instruction.getTenant().getId();
    Long groupId = instruction.getGroup().getId();
    Long ancestorGroupId = 6L;
    Long pathGroupId = 8L;
    Long startTimeLte = instruction.getStartTime();
    Long startTimeGte = instruction.getStartTime() + 1L;
    Long endTimeLte = instruction.getEndTime();
    Long endTimeGte = instruction.getEndTime() + 1L;
    Boolean endTimeNotNull = true;
    Boolean endTimeNull = true;
    InstructionStatus status = instruction.getStatus();
    InstructionStatus statusNot = InstructionStatus.CLOSED;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long createdBy = instruction.getCreatedBy().getId();
    String filter = null;
    String search = "instrction one";
    Long pageNumber = 0L;
    Long pageSize = 20L;

    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(Sort.Direction.DESC, "startTime"));

    Page<Instruction> page = new PageImpl<>(list, pageable, list.size());

    given(instructionRepository.findAll(any(Specification.class), eq(pageable))).willReturn(page);

    Page<Instruction> result =
        instructionService.readPage(
            tenantId,
            groupId,
            ancestorGroupId,
            pathGroupId,
            startTimeLte,
            startTimeGte,
            endTimeLte,
            endTimeGte,
            endTimeNotNull,
            endTimeNull,
            status,
            statusNot,
            candidateGroups,
            createdBy,
            filter,
            search,
            null,
            pageNumber,
            pageSize);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    then(result.getContent().get(0).getId()).isEqualTo(instruction.getId());
    then(result.getContent().get(0).getSid()).isEqualTo(instruction.getSid());
    then(result.getContent().get(0).getDescription()).isEqualTo(instruction.getDescription());
    then(result.getContent().get(0).getLocked()).isFalse();
    then(result.getContent().get(0).getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getContent().get(0).getStartTime()).isEqualTo(instruction.getStartTime());
    then(result.getContent().get(0).getEndTime()).isEqualTo(instruction.getEndTime());
    then(result.getContent().get(0).getTenant().getId()).isEqualTo(instruction.getTenant().getId());
    then(result.getContent().get(0).getGroup().getId()).isEqualTo(instruction.getGroup().getId());
    then(result.getContent().get(0).getCreationDate()).isEqualTo(instruction.getCreationDate());
    then(result.getContent().get(0).getModifiedDate()).isEqualTo(instruction.getModifiedDate());
    then(result.getContent().get(0).getCreatedBy().getId())
        .isEqualTo(instruction.getCreatedBy().getId());
    then(result.getContent().get(0).getModifiedBy().getId())
        .isEqualTo(instruction.getModifiedBy().getId());
  }

  @Test
  public void create_withInstruction_returnsInstruction() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    ProcessInstanceBuilder instanceBuilder = mock(ProcessInstanceBuilder.class);
    ProcessInstance processInstance = mock(ProcessInstance.class);

    given(instanceBuilder.start()).willReturn(processInstance);
    given(instructionRepository.save(instruction)).willReturn(instruction);
    given(runtimeService.createProcessInstanceBuilder()).willReturn(instanceBuilder);
    given(processInstance.getId()).willReturn("mockedProcessInstanceId");

    Long result = instructionService.create(instruction);

    verify(instructionRepository).save(instruction);
    verify(runtimeService).setVariables(eq(instruction.getProcessInstance().getId()), anyMap());
    verify(instanceBuilder).start();
    then(result).isEqualTo(instruction.getId());
  }

  @Test
  public void create_withInstructionNoEndTime_returnsInstruction() {
    Instruction instruction = InstructionMother.aSavedInstruction().withEndTime(null).build();
    ProcessInstanceBuilder instanceBuilder = mock(ProcessInstanceBuilder.class);
    ProcessInstance processInstance = mock(ProcessInstance.class);

    given(instanceBuilder.start()).willReturn(processInstance);
    given(instructionRepository.save(instruction)).willReturn(instruction);
    given(runtimeService.createProcessInstanceBuilder()).willReturn(instanceBuilder);
    given(processInstance.getId()).willReturn("mockedProcessInstanceId");

    Long result = instructionService.create(instruction);

    verify(instructionRepository).save(instruction);
    verify(runtimeService).setVariables(eq(instruction.getProcessInstance().getId()), anyMap());
    verify(instanceBuilder).start();
    then(result).isEqualTo(instruction.getId());
  }

  @Test
  public void update_withInstructionSameTimes_returnsInstruction() {
    Instruction newInstruction = InstructionMother.aSavedInstruction().build();
    Instruction oldInstruction =
        InstructionMother.aSavedInstruction()
            .withStartTime(newInstruction.getStartTime())
            .withEndTime(newInstruction.getEndTime())
            .build();
    newInstruction.getProcessInstance().setId("processInstanceId");

    given(
            instructionRepository.findByIdAndTenantId(
                newInstruction.getId(), newInstruction.getTenant().getId()))
        .willReturn(Optional.of(oldInstruction));

    Instruction result = instructionService.update(newInstruction);

    then(result.getId()).isEqualTo(newInstruction.getId());
    then(result.getSid()).isEqualTo(newInstruction.getSid());
    then(result.getDescription()).isEqualTo(newInstruction.getDescription());
    then(result.getLocked()).isFalse();
    then(result.getTenant().getId()).isEqualTo(newInstruction.getTenant().getId());
    then(result.getStartTime()).isEqualTo(newInstruction.getStartTime());
    then(result.getEndTime()).isEqualTo(newInstruction.getEndTime());
    then(result.getTenant().getId()).isEqualTo(newInstruction.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(newInstruction.getGroup().getId());
    then(result.getCreationDate()).isEqualTo(newInstruction.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(newInstruction.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(newInstruction.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(newInstruction.getModifiedBy().getId());
    verify(applicationEventPublisher)
        .publishEvent(new InstructionUpdatedEvent(newInstruction.getId()));
  }

  @Test
  public void update_withInstructionNoEndTime_returnsInstruction() {
    Instruction newInstruction = InstructionMother.aSavedInstruction().withEndTime(null).build();
    Instruction oldInstruction =
        InstructionMother.aSavedInstruction()
            .withStartTime(newInstruction.getStartTime())
            .withEndTime(newInstruction.getEndTime())
            .build();
    newInstruction.getProcessInstance().setId("processInstanceId");

    given(
            instructionRepository.findByIdAndTenantId(
                newInstruction.getId(), newInstruction.getTenant().getId()))
        .willReturn(Optional.of(oldInstruction));

    Instruction result = instructionService.update(newInstruction);

    then(result.getId()).isEqualTo(newInstruction.getId());
    then(result.getSid()).isEqualTo(newInstruction.getSid());
    then(result.getDescription()).isEqualTo(newInstruction.getDescription());
    then(result.getLocked()).isFalse();
    then(result.getTenant().getId()).isEqualTo(newInstruction.getTenant().getId());
    then(result.getStartTime()).isEqualTo(newInstruction.getStartTime());
    then(result.getEndTime()).isEqualTo(newInstruction.getEndTime());
    then(result.getTenant().getId()).isEqualTo(newInstruction.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(newInstruction.getGroup().getId());
    then(result.getCreationDate()).isEqualTo(newInstruction.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(newInstruction.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(newInstruction.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(newInstruction.getModifiedBy().getId());
    verify(applicationEventPublisher)
        .publishEvent(new InstructionUpdatedEvent(newInstruction.getId()));
  }

  @Test
  public void update_withInstructionDifferentTimes_returnsInstruction() {
    Instruction newInstruction = InstructionMother.aSavedInstruction().build();
    Instruction oldInstruction =
        InstructionMother.aSavedInstruction()
            .withStartTime(992233L)
            .withEndTime(66253822L)
            .withProcessInstance(
                ProcessInstanceCustomMother.aProcessInstanceCustom()
                    .withId("processInstanceId")
                    .withProcessInstanceId("processInstanceId")
                    .build())
            .build();

    given(
            instructionRepository.findByIdAndTenantId(
                newInstruction.getId(), newInstruction.getTenant().getId()))
        .willReturn(Optional.of(oldInstruction));
    ExecutionQuery executionQueryMock = mock(ExecutionQuery.class);
    when(runtimeService.createExecutionQuery()).thenReturn(executionQueryMock);
    when(executionQueryMock.processInstanceId(null)).thenReturn(executionQueryMock);
    Execution execution = mock(Execution.class);
    List<Execution> executionListMock = List.of(execution);
    when(executionQueryMock.list()).thenReturn(executionListMock);
    given(execution.getActivityId()).willReturn("123");
    given(execution.getId()).willReturn("321");
    ChangeActivityStateBuilder changeActivityStateBuilder = mock(ChangeActivityStateBuilder.class);
    given(runtimeService.createChangeActivityStateBuilder()).willReturn(changeActivityStateBuilder);

    Instruction result = instructionService.update(newInstruction);
    then(result.getId()).isEqualTo(newInstruction.getId());
    then(result.getSid()).isEqualTo(newInstruction.getSid());
    then(result.getDescription()).isEqualTo(newInstruction.getDescription());
    then(result.getLocked()).isFalse();
    then(result.getTenant().getId()).isEqualTo(newInstruction.getTenant().getId());
    then(result.getStartTime()).isEqualTo(newInstruction.getStartTime());
    then(result.getEndTime()).isEqualTo(newInstruction.getEndTime());
    then(result.getTenant().getId()).isEqualTo(newInstruction.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(newInstruction.getGroup().getId());
    then(result.getCreationDate()).isEqualTo(newInstruction.getCreationDate());
    then(result.getModifiedDate()).isEqualTo(newInstruction.getModifiedDate());
    then(result.getCreatedBy().getId()).isEqualTo(newInstruction.getCreatedBy().getId());
    then(result.getModifiedBy().getId()).isEqualTo(newInstruction.getModifiedBy().getId());
    verify(changeActivityStateBuilder).changeState();
    verify(applicationEventPublisher)
        .publishEvent(new InstructionUpdatedEvent(newInstruction.getId()));
  }

  @Test
  public void update_withLocked_throwsException() {
    Instruction instruction = InstructionMother.aSavedInstruction().withLocked(true).build();
    Long id = instruction.getId();

    InstructionLockedException error =
        assertThrows(
            InstructionLockedException.class, () -> instructionService.update(instruction));

    then(error.getMessage()).isEqualTo("Instruction with id " + id + " is locked");
  }

  @Test
  public void checkDeletable_withNull_returnsTrue() {
    then(instructionService.checkDeletable(null)).isTrue();
  }

  @Test
  public void cancel_withInstruction_StatusCanceled() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    instructionService.cancel(instruction);

    then(instruction.getLocked()).isTrue();
    then(instruction.getStatus()).isEqualTo(InstructionStatus.CANCELED);
    verify(applicationEventPublisher)
        .publishEvent(new InstructionCanceledEvent(instruction.getId()));
  }

  @Test
  public void cancel_withInstructionAlreadyCanceled_StatusCanceled() {
    Instruction instruction =
        InstructionMother.aSavedInstruction().withStatus(InstructionStatus.CANCELED).build();

    instructionService.cancel(instruction);

    then(instruction.getLocked()).isTrue();
    then(instruction.getStatus()).isEqualTo(InstructionStatus.CANCELED);
  }

  @Test
  public void activateInstruction_withIdAndTenant_statusToActivated() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.activateInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getStatus()).isEqualTo(InstructionStatus.ACTIVE);
    verify(instructionRepository).save(instruction);
    verify(applicationEventPublisher)
        .publishEvent(new InstructionActivatedEvent(instruction.getId()));
  }

  @Test
  public void activateInstruction_withIdAndTenantALreadyActive_returnsActive() {
    Instruction instruction =
        InstructionMother.aSavedInstruction().withStatus(InstructionStatus.ACTIVE).build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.activateInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getStatus()).isEqualTo(InstructionStatus.ACTIVE);
  }

  @Test
  public void closeInstruction_withIdAndTenant_statusToClosed() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.closeInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getStatus()).isEqualTo(InstructionStatus.CLOSED);
    verify(instructionRepository).save(instruction);
    verify(applicationEventPublisher).publishEvent(new InstructionClosedEvent(instruction.getId()));
  }

  @Test
  public void closeInstruction_withIdAndTenantIdAlreadyClosed_statusToClosed() {
    Instruction instruction =
        InstructionMother.aSavedInstruction().withStatus(InstructionStatus.CLOSED).build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.closeInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getStatus()).isEqualTo(InstructionStatus.CLOSED);
  }

  @Test
  public void planInstruction_withIdAndTenant_setStatusToPlanned() {
    Instruction instruction =
        InstructionMother.aSavedInstruction().withStatus(InstructionStatus.ACTIVE).build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.planInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getStatus()).isEqualTo(InstructionStatus.PLANNED);
    verify(instructionRepository).save(instruction);
    verify(applicationEventPublisher)
        .publishEvent(new InstructionPlannedEvent(instruction.getId()));
  }

  @Test
  public void planInstruction_withIdAndTenantAlreadyPlanned_setStatusToPlanned() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.planInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getStatus()).isEqualTo(InstructionStatus.PLANNED);
  }

  @Test
  public void lockInstruction_withIds_lockedToTrue() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    given(
            instructionRepository.findByIdAndTenantId(
                instruction.getId(), instruction.getTenant().getId()))
        .willReturn(Optional.of(instruction));

    instructionService.lockInstruction(instruction.getId(), instruction.getTenant().getId());
    then(instruction.getLocked()).isTrue();
    verify(instructionRepository).save(instruction);
  }

  @Test
  public void delete_withDeletableInstruction_deletes() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    instructionService.delete(instruction);
    verify(instructionRepository).delete(instruction);
  }

  @Test
  public void generatePdfContext_withTimeZoneAndInstruction_returnsContext() {
    Instruction instruction = InstructionMother.aSavedInstruction().build();

    Context result = instructionService.generatePdfContext(instruction, "UTC");

    then(result.getVariableNames().size()).isEqualTo(2);
    then(result.getVariable("instruction")).isEqualTo(instruction);
    then(result.getVariable("timeZone")).isEqualTo("UTC");
  }

  @Test
  public void generatePdf_withInstructionAndTimezone_returnsOutput() throws IOException {
    Instruction instruction = InstructionMother.aSavedInstruction().build();
    given(templateEngine.process(eq("pdf/instruction.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

    instructionService.generatePdf(instruction, "UTC", outputStream);

    then(outputStream.size()).isGreaterThan(0);
  }
}
