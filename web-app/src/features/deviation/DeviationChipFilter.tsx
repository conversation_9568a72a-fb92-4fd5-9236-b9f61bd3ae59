import React, { useState, useEffect } from 'react';
import { <PERSON>, Stack, IconButton, Popover, FormControlLabel, Tooltip, Typography, Checkbox } from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { useAppDispatch, useAppSelector } from '../../store';
import { setDeviationViewState } from './deviationSlice';
import { User } from '../user/userTypes';
import {
  DeviationCandidateGroup,
  DeviationCandidateGroupDisplayMap,
  DeviationColumn,
  DeviationColumnDefaults,
  DeviationColumnDisplayMap,
  DeviationColumnSetting,
} from './deviationTypes';

const mergeColumns = (
  persistedColumns: DeviationColumnSetting[] = [],
  defaultColumns: DeviationColumnSetting[] = []
): DeviationColumnSetting[] => {
  // Keep only the persisted columns that still exist in the defaults.
  const validPersisted = persistedColumns.filter((pc) => defaultColumns.some((dc) => dc.column === pc.column));

  // Create a set of the column keys in the valid persisted list.
  const persistedSet = new Set(validPersisted.map((pc) => pc.column));

  // Find any default columns that the customer hasn't chosen yet.
  const newDefaults = defaultColumns.filter((dc) => !persistedSet.has(dc.column));

  // Return the persisted order first, then add new columns at the end.
  return [...validPersisted, ...newDefaults];
};

interface DeviationChipFilterProps {
  me?: User;
  resetPageNumber: () => void;
}

function DeviationChipFilter({ me, resetPageNumber }: DeviationChipFilterProps) {
  const deviationViewState = useAppSelector((state) => state.deviation.deviationViewState);
  const dispatch = useAppDispatch();

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
  const [columnsOrder, setColumnsOrder] = useState<DeviationColumnSetting[]>(
    mergeColumns(deviationViewState.columns, DeviationColumnDefaults)
  );

  useEffect(() => {
    setColumnsOrder(mergeColumns(deviationViewState.columns, DeviationColumnDefaults));
  }, [deviationViewState.columns]);

  const handleFineTuneClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'column-popover' : undefined;

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      // Update view state with the new ordering.
      dispatch(setDeviationViewState({ ...deviationViewState, columns: newOrder }));
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: DeviationColumn) => {
    // Toggle the hidden flag for the selected column.
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    dispatch(setDeviationViewState({ ...deviationViewState, columns: newOrder }));
  };

  return (
    <Stack direction="row" flexWrap={{ xs: 'nowrap', sm: 'wrap' }} overflow={{ xs: 'scroll', sm: 'unset' }}>
      <Chip
        label="Created by me"
        sx={{ mb: 1, mr: 1 }}
        color={deviationViewState.createdBy && deviationViewState.createdBy.id === me?.id ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...deviationViewState };
          if (newState.createdBy && newState.createdBy.id === me?.id) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          dispatch(setDeviationViewState(newState));
          resetPageNumber();
        }}
      />
      {Object.values(DeviationCandidateGroup).map((g) => (
        <Chip
          sx={{ mb: 1, mr: 1 }}
          label={DeviationCandidateGroupDisplayMap[g]}
          color={deviationViewState.candidateGroups?.find((c) => c === g) ? 'primary' : 'default'}
          onClick={() => {
            const newState = { ...deviationViewState };
            if (!!newState.candidateGroups && !!newState.candidateGroups.find((c) => c === g)) {
              newState.candidateGroups = newState.candidateGroups.filter((c) => c !== g);
            } else {
              const newGroups = newState.candidateGroups || [];
              newState.candidateGroups = newGroups.concat(g);
            }
            dispatch(setDeviationViewState(newState));
            resetPageNumber();
          }}
        />
      ))}
      <Tooltip title="View options">
        <IconButton size="small" sx={{ p: 0.5, mb: 1, mr: 1 }} onClick={handleFineTuneClick}>
          <TuneIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              px: 2,
              py: 1,
              maxWidth: 300,
            },
          },
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Columns</Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={DeviationColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}

export default DeviationChipFilter;
