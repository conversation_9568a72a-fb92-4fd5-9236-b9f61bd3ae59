import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs, ToggleButton, ToggleButtonGroup, Tooltip } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import ViewListIcon from '@mui/icons-material/ViewList';
import MapIcon from '@mui/icons-material/Map';
import {
  DeviationColumn,
  DeviationColumnDefaults,
  DeviationColumnDisplayMap,
  DeviationFieldSortMap,
  DeviationParams,
  DeviationRead,
  DeviationSort,
  DeviationSortField,
  DeviationStatus,
  DeviationStatusDisplayMap,
} from './deviationTypes';
import { useAppDispatch, useAppSelector } from '../../store';
import { useGetDeviationsQuery } from './deviationApi';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import DeviationFilterBar from './DeviationFilterBar';
import { GuardResult } from '../guard/guardHooks';
import { PermissionType } from '../guard/guardTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import Guard from '../guard/Guard';
import { UserRole, UserDisplay } from '../user/userTypes';
import { setDeviationViewState } from './deviationSlice';
import { useGetCurrentUserQuery } from '../user/userApi';
import DeviationChipFilter from './DeviationChipFilter';
import DeviationMap from './DeviationMap';
import { LocationFilterMode } from '../location/locationTypes';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import UserCell from '../../components/UserCell';
import LocationCell from '../../components/LocationCell';
import DeviationNameCell from './cell/DeviationNameCell';
import DeviationStatusCell from './cell/DeviationStatusCell';
import DeviationDateCell from './cell/DeviationDateCell';
import { GroupDisplay } from '../group/groupTypes';
import { LocationDisplay } from '../location/locationTypes';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getDeviationUrl = (deviationId: number) => `${deviationId}`;

const getGridModelFromSort = (sort: DeviationSort[]): GridSortModel =>
  sort.map((s) => ({
    field:
      Object.keys(DeviationFieldSortMap).find((key) => DeviationFieldSortMap[key as keyof DeviationRead] === s.field) || s.field,
    sort: s.direction,
  }));

const getSortFromGridModel = (gridModel: GridSortModel): DeviationSort[] =>
  gridModel.map((model) => ({
    field: DeviationFieldSortMap[model.field as keyof DeviationRead] || (model.field as DeviationSortField),
    direction: model.sort || 'asc',
  }));

const columnDefaults: Record<DeviationColumn, GridColDef<DeviationRead>> = {
  [DeviationColumn.SID]: {
    field: DeviationColumn.SID,
    headerName: DeviationColumnDisplayMap[DeviationColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, number, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getDeviationUrl(params.row.id)),
  },
  [DeviationColumn.DESCRIPTION]: {
    field: DeviationColumn.DESCRIPTION,
    headerName: DeviationColumnDisplayMap[DeviationColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(DeviationNameCell(params), getDeviationUrl(params.row.id)),
  },
  [DeviationColumn.GROUP]: {
    field: DeviationColumn.GROUP,
    headerName: DeviationColumnDisplayMap[DeviationColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getDeviationUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [DeviationColumn.CREATED_BY]: {
    field: DeviationColumn.CREATED_BY,
    headerName: DeviationColumnDisplayMap[DeviationColumn.CREATED_BY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getDeviationUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [DeviationColumn.LOCATION]: {
    field: DeviationColumn.LOCATION,
    headerName: DeviationColumnDisplayMap[DeviationColumn.LOCATION],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(LocationCell(params), getDeviationUrl(params.row.id)),
    valueGetter: (value: LocationDisplay) => (value ? value.name : ''),
  },
  [DeviationColumn.STATUS]: {
    field: DeviationColumn.STATUS,
    headerName: DeviationColumnDisplayMap[DeviationColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, DeviationStatus, string>) =>
      DataGridCellLinkWrapper(DeviationStatusCell(params), getDeviationUrl(params.row.id)),
  },
  [DeviationColumn.DATE]: {
    field: DeviationColumn.DATE,
    headerName: DeviationColumnDisplayMap[DeviationColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, number, string>) =>
      DataGridCellLinkWrapper(DeviationDateCell(params), getDeviationUrl(params.row.id)),
    valueFormatter: (value: number) => (value ? new Date(value).toDateString() : ''),
  },
};

function DeviationListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const deviationViewState = useAppSelector((state) => state.deviation.deviationViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(deviationViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = deviationViewState.columns ? deviationViewState.columns : DeviationColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [deviationViewState.columns]);

  const getCandidateGroups = () =>
    deviationViewState?.candidateGroups?.length && deviationViewState.candidateGroups.length > 0
      ? deviationViewState?.candidateGroups
      : undefined;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || deviationViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${DeviationStatus.CLOSED}&statusNot=${DeviationStatus.CANCELED}`;
  };

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newViewMode: 'table' | 'map' | null) => {
    if (newViewMode !== null) {
      // Update Redux state directly
      dispatch(setDeviationViewState({ ...deviationViewState, viewMode: newViewMode }));
    }
  };

  const [params, setParams] = useState<DeviationParams>({
    pathGroupId: Number(groupId),
    search: deviationViewState.search,
    groupId: deviationViewState?.group?.id,
    status: deviationViewState?.status,
    createdBy: deviationViewState?.createdBy?.id,
    filter: getFilter(),
    candidateGroups: getCandidateGroups(),
    pageSize: defaultPageSize,
    pageNumber: 0,
    ancestorLocationId:
      deviationViewState?.locationFilterMode === LocationFilterMode.UNDER
        ? deviationViewState?.location?.id
        : undefined,
    locationId:
      deviationViewState?.locationFilterMode === LocationFilterMode.EQUALS
        ? deviationViewState?.location?.id
        : undefined,
  });

  const { data, isLoading, error } = useGetDeviationsQuery(params);
  const [page, setPage] = useState<number>(0);
  // ? Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows = page > 0 ? (data?.pageSize || 0) - (data?.content.length || 0) : 0;

  useEffect(() => {
    if (deviationViewState) {
      setParams((prev) => ({
        ...prev,
        search: deviationViewState?.search,
        groupId: deviationViewState?.group?.id,
        status: deviationViewState?.status,
        createdBy: deviationViewState.createdBy?.id,
        candidateGroups: getCandidateGroups(),
        ancestorLocationId:
          deviationViewState?.locationFilterMode === LocationFilterMode.UNDER
            ? deviationViewState?.location?.id
            : undefined,
        locationId:
          deviationViewState?.locationFilterMode === LocationFilterMode.EQUALS
            ? deviationViewState?.location?.id
            : undefined,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviationViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    dispatch(
      setDeviationViewState({
        ...deviationViewState,
        listView: view,
        candidateGroups: getCandidateGroups(),
      })
    );
    const newParams = { ...params };
    newParams.pageNumber = 0;
    newParams.filter = getFilter(view);
    setParams(newParams);
  };

  const handleChangePage = (newPage: number) => {
    setPage(newPage);
    const newParams = { ...params };
    newParams.pageNumber = newPage;
    setParams(newParams);
  };

  const resetPageNumber = () => {
    handleChangePage(0);
  };

  const canCreateDeviation = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.DEVIATION_CREATE);

  return (
    <ErrorGate error={error}>
      <PageTitle page="Deviations" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={deviationViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Box display="flex" gap={2}>
            <ToggleButtonGroup
              value={deviationViewState.viewMode || 'table'}
              exclusive
              onChange={handleViewModeChange}
              aria-label="view mode"
              size="small"
            >
              <ToggleButton value="table" aria-label="table view">
                <Tooltip title="Table View">
                  <ViewListIcon />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="map" aria-label="map view">
                <Tooltip title="Map View">
                  <MapIcon />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <Guard hasAccess={canCreateDeviation}>
              <ResponsiveButton component={Link} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Add deviation
              </ResponsiveButton>
            </Guard>
          </Box>
        </Box>
        <DeviationChipFilter me={me} resetPageNumber={resetPageNumber} />
        <DeviationFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel sx={{ px: 0, pt: 1, pb: 0 }} value="0">
          {(deviationViewState.viewMode || 'table') === 'table' ? (
            <>
              <TableContainer component={Paper} elevation={4}>
                <Table sx={{ whiteSpace: 'nowrap' }}>
                  <TableHead>
                    <TableRow>
                      <TableCell width="75">ID</TableCell>
                      <TableCell sx={{ maxWidth: { xs: '350px', lg: '500px', xl: '700px' } }}>Description</TableCell>
                      <TableCell width="200">By group</TableCell>
                      <TableCell width="200">By user</TableCell>
                      <TableCell width="200">Location</TableCell>
                      <TableCell width="180">Status</TableCell>
                      <TableCell width="220">Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {!isLoading &&
                      data &&
                      data.content.length > 0 &&
                      data.content.map((deviation) => (
                        <TableRow key={deviation.id} hover>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{ maxWidth: '75px' }}
                            tableCellProps={{ style: { verticalAlign: 'top ' } }}
                          >
                            {deviation.sid}
                          </TableCellLink>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{
                              maxWidth: { xs: '350px', lg: '500px', xl: 'unset' },
                              minWidth: { xs: '350px', lg: '500px', xl: '700px' },
                              whiteSpace: 'pre-wrap',
                            }}
                          >
                            {deviation.description}
                          </TableCellLink>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{ maxWidth: '200px' }}
                            tableCellProps={{ style: { verticalAlign: 'top ' } }}
                          >
                            <PeopleAltOutlinedIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                            {deviation.group.name}
                          </TableCellLink>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{ maxWidth: '200px' }}
                            tableCellProps={{ style: { verticalAlign: 'top ' } }}
                          >
                            <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                            {deviation?.createdBy.fullName}
                          </TableCellLink>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{ maxWidth: '200px' }}
                            tableCellProps={{ style: { verticalAlign: 'top ' } }}
                          >
                            <LocationOnIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                            {deviation?.location.name}
                          </TableCellLink>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{ maxWidth: '180px' }}
                            tableCellProps={{ style: { verticalAlign: 'top ' } }}
                          >
                            {deviation?.locked ? (
                              <LockIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
                            ) : (
                              <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
                            )}{' '}
                            {DeviationStatusDisplayMap[deviation.status]}
                          </TableCellLink>
                          <TableCellLink
                            to={`${deviation.id}`}
                            sx={{ maxWidth: '220px' }}
                            tableCellProps={{ style: { verticalAlign: 'top ' } }}
                          >
                            {' '}
                            <DateRangeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                            {`${getDatePlusTimeString(new Date(deviation.date))}`}
                          </TableCellLink>
                        </TableRow>
                      ))}
                    {!isLoading && (!data || data.content.length === 0) && (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          No deviations found.
                        </TableCell>
                      </TableRow>
                    )}
                    {isLoading && (
                      <TableRow>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                        <TableCell>
                          <Skeleton />
                        </TableCell>
                      </TableRow>
                    )}
                    {emptyRows > 0 && (
                      <TableRow style={{ height: 53 * emptyRows }}>
                        <TableCell colSpan={7} />
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box display="flex" justifyContent="flex-end">
                <TablePagination
                  component="div"
                  sx={{ border: 0 }}
                  rowsPerPage={defaultPageSize}
                  rowsPerPageOptions={[]}
                  count={!isLoading && data ? data.total : 0}
                  page={page}
                  onPageChange={(_, pageNumber) => handleChangePage(pageNumber)}
                />
              </Box>
            </>
          ) : (
            <DeviationMap groupId={groupId || ''} params={params} listView={deviationViewState.listView} />
          )}
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}
export default DeviationListPage;
