import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs, ToggleButton, ToggleButtonGroup, Tooltip } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import ViewListIcon from '@mui/icons-material/ViewList';
import MapIcon from '@mui/icons-material/Map';
import {
  DeviationColumn,
  DeviationColumnDefaults,
  DeviationColumnDisplayMap,
  DeviationFieldSortMap,
  DeviationParams,
  DeviationRead,
  DeviationSort,
  DeviationSortField,
  DeviationStatus,
} from './deviationTypes';
import { useAppDispatch, useAppSelector } from '../../store';
import { useGetDeviationsQuery } from './deviationApi';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import DeviationFilterBar from './DeviationFilterBar';
import { GuardResult } from '../guard/guardHooks';
import { PermissionType } from '../guard/guardTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import Guard from '../guard/Guard';
import { UserRole, UserDisplay } from '../user/userTypes';
import { setDeviationViewState } from './deviationSlice';
import { useGetCurrentUserQuery } from '../user/userApi';
import DeviationChipFilter from './DeviationChipFilter';
import DeviationMap from './DeviationMap';
import { LocationFilterMode, LocationDisplay } from '../location/locationTypes';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import UserCell from '../../components/UserCell';
import LocationCell from '../../components/LocationCell';
import DeviationNameCell from './cell/DeviationNameCell';
import DeviationStatusCell from './cell/DeviationStatusCell';
import DeviationDateCell from './cell/DeviationDateCell';
import { GroupDisplay } from '../group/groupTypes';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getDeviationUrl = (deviationId: number) => `${deviationId}`;

const getGridModelFromSort = (sort: DeviationSort[]): GridSortModel =>
  sort.map((s) => ({
    field:
      Object.keys(DeviationFieldSortMap).find((key) => DeviationFieldSortMap[key as keyof DeviationRead] === s.field) || s.field,
    sort: s.direction,
  }));

const getSortFromGridModel = (gridModel: GridSortModel): DeviationSort[] =>
  gridModel.map((model) => ({
    field: DeviationFieldSortMap[model.field as keyof DeviationRead] || (model.field as DeviationSortField),
    direction: model.sort || 'asc',
  }));

const columnDefaults: Record<DeviationColumn, GridColDef<DeviationRead>> = {
  [DeviationColumn.SID]: {
    field: DeviationColumn.SID,
    headerName: DeviationColumnDisplayMap[DeviationColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, number, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getDeviationUrl(params.row.id)),
  },
  [DeviationColumn.DESCRIPTION]: {
    field: DeviationColumn.DESCRIPTION,
    headerName: DeviationColumnDisplayMap[DeviationColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(DeviationNameCell(params), getDeviationUrl(params.row.id)),
  },
  [DeviationColumn.GROUP]: {
    field: DeviationColumn.GROUP,
    headerName: DeviationColumnDisplayMap[DeviationColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getDeviationUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [DeviationColumn.CREATED_BY]: {
    field: DeviationColumn.CREATED_BY,
    headerName: DeviationColumnDisplayMap[DeviationColumn.CREATED_BY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getDeviationUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [DeviationColumn.LOCATION]: {
    field: DeviationColumn.LOCATION,
    headerName: DeviationColumnDisplayMap[DeviationColumn.LOCATION],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, string, string>) =>
      DataGridCellLinkWrapper(LocationCell(params), getDeviationUrl(params.row.id)),
    valueGetter: (value: LocationDisplay) => (value ? value.name : ''),
  },
  [DeviationColumn.STATUS]: {
    field: DeviationColumn.STATUS,
    headerName: DeviationColumnDisplayMap[DeviationColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, DeviationStatus, string>) =>
      DataGridCellLinkWrapper(DeviationStatusCell(params), getDeviationUrl(params.row.id)),
  },
  [DeviationColumn.DATE]: {
    field: DeviationColumn.DATE,
    headerName: DeviationColumnDisplayMap[DeviationColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<DeviationRead, number, string>) =>
      DataGridCellLinkWrapper(DeviationDateCell(params), getDeviationUrl(params.row.id)),
    valueFormatter: (value: number) => (value ? new Date(value).toDateString() : ''),
  },
};

function DeviationListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const deviationViewState = useAppSelector((state) => state.deviation.deviationViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(deviationViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = deviationViewState.columns ? deviationViewState.columns : DeviationColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [deviationViewState.columns]);

  const getCandidateGroups = () =>
    deviationViewState?.candidateGroups?.length && deviationViewState.candidateGroups.length > 0
      ? deviationViewState?.candidateGroups
      : undefined;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || deviationViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${DeviationStatus.CLOSED}&statusNot=${DeviationStatus.CANCELED}`;
  };

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newViewMode: 'table' | 'map' | null) => {
    if (newViewMode !== null) {
      // Update Redux state directly
      dispatch(setDeviationViewState({ ...deviationViewState, viewMode: newViewMode }));
    }
  };

  const getSortString = (sort: DeviationSort[]): string | undefined => {
    if (!sort || sort.length === 0) return undefined;
    return sort.map((s) => `${s.field}:${s.direction}`).join(',');
  };

  const [params, setParams] = useState<DeviationParams>({
    pathGroupId: Number(groupId),
    search: deviationViewState.search,
    groupId: deviationViewState?.group?.id,
    status: deviationViewState?.status,
    createdBy: deviationViewState?.createdBy?.id,
    filter: getFilter(),
    candidateGroups: getCandidateGroups(),
    sort: getSortString(deviationViewState?.sort || []),
    pageSize,
    pageNumber: page,
    ancestorLocationId:
      deviationViewState?.locationFilterMode === LocationFilterMode.UNDER
        ? deviationViewState?.location?.id
        : undefined,
    locationId:
      deviationViewState?.locationFilterMode === LocationFilterMode.EQUALS
        ? deviationViewState?.location?.id
        : undefined,
  });

  const { data, isLoading, error } = useGetDeviationsQuery(params);

  useEffect(() => {
    if (deviationViewState) {
      setParams((prev) => ({
        ...prev,
        search: deviationViewState?.search,
        groupId: deviationViewState?.group?.id,
        status: deviationViewState?.status,
        createdBy: deviationViewState.createdBy?.id,
        candidateGroups: getCandidateGroups(),
        sort: getSortString(deviationViewState?.sort || []),
        pageSize,
        pageNumber: page,
        ancestorLocationId:
          deviationViewState?.locationFilterMode === LocationFilterMode.UNDER
            ? deviationViewState?.location?.id
            : undefined,
        locationId:
          deviationViewState?.locationFilterMode === LocationFilterMode.EQUALS
            ? deviationViewState?.location?.id
            : undefined,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviationViewState, page, pageSize]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    setPage(0);
    dispatch(
      setDeviationViewState({
        ...deviationViewState,
        listView: view,
      })
    );
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setDeviationViewState({
        ...deviationViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  const canCreateDeviation = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.DEVIATION_CREATE);

  return (
    <ErrorGate error={error}>
      <PageTitle page="Deviations" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={deviationViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Box display="flex" gap={2}>
            <ToggleButtonGroup
              value={deviationViewState.viewMode || 'table'}
              exclusive
              onChange={handleViewModeChange}
              aria-label="view mode"
              size="small"
            >
              <ToggleButton value="table" aria-label="table view">
                <Tooltip title="Table View">
                  <ViewListIcon />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="map" aria-label="map view">
                <Tooltip title="Map View">
                  <MapIcon />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <Guard hasAccess={canCreateDeviation}>
              <ResponsiveButton component={Link} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Add deviation
              </ResponsiveButton>
            </Guard>
          </Box>
        </Box>
        <DeviationChipFilter me={me} resetPageNumber={resetPageNumber} />
        <DeviationFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
          value="0"
        >
          {(deviationViewState.viewMode || 'table') === 'table' ? (
            <Paper elevation={4}>
              <Box
                sx={{
                  height: 'calc(100vh - 269px)',
                  overflow: 'hidden',
                  '@media (max-height: 600px)': {
                    height: '100%',
                  },
                }}
              >
                <DataGrid
                  rows={data?.content || []}
                  columns={columns}
                  rowCount={data?.total || 0}
                  loading={isLoading}
                  disableColumnMenu
                  pagination
                  paginationMode="server"
                  paginationModel={{ page, pageSize }}
                  onPaginationModelChange={handlePaginationChange}
                  sortingMode="server"
                  sortModel={sortModel}
                  onSortModelChange={handleSortModelChange}
                  disableRowSelectionOnClick
                  slots={{
                    noRowsOverlay: NoRowsOverlay,
                  }}
                  onColumnWidthChange={(columnParams) => {
                    const newViewState = { ...deviationViewState };
                    if (newViewState.columns) {
                      // Clone the columns array to avoid direct mutation.
                      const updatedColumns = [...newViewState.columns];
                      // Find the column to update.
                      const columnToUpdate = updatedColumns.find((c) => c.column === columnParams.colDef.field);
                      if (columnToUpdate) {
                        // Get the index of the column and update immutably.
                        const index = updatedColumns.indexOf(columnToUpdate);
                        updatedColumns[index] = { ...columnToUpdate, width: columnParams.width };
                      }
                      newViewState.columns = updatedColumns;
                    }
                    dispatch(setDeviationViewState(newViewState));
                  }}
                  slotProps={{
                    loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                    noRowsOverlay: { title: 'No deviations found' },
                  }}
                />
              </Box>
            </Paper>
          ) : (
            <DeviationMap groupId={groupId || ''} params={params} listView={deviationViewState.listView} />
          )}
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}
export default DeviationListPage;
