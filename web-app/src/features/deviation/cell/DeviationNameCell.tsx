import { GridRenderCellParams } from '@mui/x-data-grid';
import { DeviationRead } from '../deviationTypes';

function DeviationNameCell(params: GridRenderCellParams<DeviationRead, string, string>) {
  return (
    <div
      style={{
        whiteSpace: 'pre-wrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        WebkitLineClamp: 3,
        WebkitBoxOrient: 'vertical',
      }}
    >
      {params.value}
    </div>
  );
}

export default DeviationNameCell;
