import { Box } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import { DeviationRead, DeviationStatus, DeviationStatusDisplayMap } from '../deviationTypes';

function DeviationStatusCell(params: GridRenderCellParams<DeviationRead, DeviationStatus, string>) {
  const deviation = params.row;
  const status = params.value;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
      {deviation?.locked ? (
        <LockIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
      ) : (
        <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
      )}
      {status ? DeviationStatusDisplayMap[status] : ''}
    </Box>
  );
}

export default DeviationStatusCell;
