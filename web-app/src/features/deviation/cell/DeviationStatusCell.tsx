import { Chip } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import { DeviationRead, DeviationStatus, DeviationStatusMeta } from '../deviationTypes';

function DeviationStatusCell(params: GridRenderCellParams<DeviationRead, DeviationStatus, string>) {
  const status = params.value;
  const meta = status ? DeviationStatusMeta[status] : undefined;

  return (
    <Chip
      label={meta?.label || status}
      size="small"
      sx={{
        backgroundColor: meta?.color,
        color: 'white',
        fontWeight: 'bold',
      }}
    />
  );
}

export default DeviationStatusCell;
