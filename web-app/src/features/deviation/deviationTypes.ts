import { themeToColor } from '../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';
import { FileDisplay } from '../file/fileTypes';
import { GroupDisplay } from '../group/groupTypes';
import { LocationDisplay, LocationFilterMode } from '../location/locationTypes';
import { User, UserDisplay } from '../user/userTypes';

export interface DeviationParams {
  groupId?: number;
  ancestorGroupId?: number;
  pathGroupId?: number;
  status?: DeviationStatus;
  statusNot?: DeviationStatus;
  createdBy?: number;
  candidateGroups?: DeviationCandidateGroup[];
  search?: string;
  filter?: string;
  sort?: string;
  pageSize?: number;
  pageNumber?: number;
  locationId?: number;
  ancestorLocationId?: number;
}

export interface DeviationPDFParams {
  id: number;
  timeZone: string;
}

export interface DeviationFormInput {
  group: GroupDisplay | null;
  location: LocationDisplay | null;
  description: string;
  files: FileDisplay[];
}

export enum DeviationCandidateGroup {
  DEVIATION_CLOSE = 'DEVIATION_CLOSE',
}

export const DeviationCandidateGroupDisplayMap: Record<DeviationCandidateGroup, string> = {
  DEVIATION_CLOSE: 'Close',
};

export interface DeviationViewState {
  listView: 'mine' | 'all';
  viewMode?: 'table' | 'map';
  group?: GroupDisplay;
  status?: DeviationStatus;
  search?: string;
  candidateGroups?: DeviationCandidateGroup[];
  createdBy?: User;
  location?: LocationDisplay;
  locationFilterMode?: LocationFilterMode;
  sort?: DeviationSort[];
  columns?: DeviationColumnSetting[];
}

export interface DeviationState {
  deviationCopy?: Partial<DeviationCopy>;
  deviationViewState: DeviationViewState;
}

export enum DeviationStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const DeviationStatusMeta: EnumMetaMap<DeviationStatus> = {
  [DeviationStatus.OPEN]: { label: 'Open', color: themeToColor('primary.main') },
  [DeviationStatus.CLOSED]: { label: 'Closed', color: themeToColor('secondary.main') },
  [DeviationStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const DeviationStatusDisplayMap = Object.fromEntries(
  Object.entries(DeviationStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<DeviationStatus, string>;

export interface DeviationRead {
  id: number;
  sid: number;
  processInstanceId: string;
  group: GroupDisplay;
  location: LocationDisplay;
  description: string;
  status: DeviationStatus;
  locked: boolean;
  createdBy: UserDisplay;
  date: number;
  files: FileDisplay[];
}

export interface DeviationCopy {
  location: LocationDisplay | null;
  description: string;
  files: FileDisplay[];
}

export interface DeviationCreate {
  group: number;
  location: number;
  description: string;
  files: number[];
}

export interface DeviationUpdate {
  id: number;
  location: number;
  description: string;
  files: number[];
}

export interface DeviationDeletable {
  deletable: boolean;
}

export enum DeviationChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const DeviationChangeTypeDisplayMap: Record<DeviationChangeType, string> = {
  INSERT: 'Deviation created',
  UPDATE: 'Deviation updated',
  DELETE: 'Deviation deleted',
};

export interface DeviationChange {
  by: UserDisplay;
  at: number;
  type: DeviationChangeType;
  oldEntity: DeviationRead;
  newEntity: DeviationRead;
}

export enum DeviationGroupBy {
  GROUP = 'DEVIATIONS_GROUP',
  CREATED_BY = 'DEVIATIONS_CREATED_BY',
  STATUS = 'DEVIATIONS_STATUS',
  LOCATION = 'DEVIATIONS_LOCATION',
  ROOT_LOCATION = 'DEVIATIONS_ROOT_LOCATION',
  CREATION_DATE_WEEK = 'DEVIATIONS_CREATION_DATE_WEEK',
}

export const DeviationGroupByDisplayMap: Record<DeviationGroupBy, string> = {
  [DeviationGroupBy.GROUP]: 'Group',
  [DeviationGroupBy.CREATED_BY]: 'User',
  [DeviationGroupBy.STATUS]: 'Status',
  [DeviationGroupBy.LOCATION]: 'Location',
  [DeviationGroupBy.ROOT_LOCATION]: 'Root Location',
  [DeviationGroupBy.CREATION_DATE_WEEK]: 'Deviation week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface DeviationGroupByFieldType {
  [DeviationGroupBy.STATUS]: DeviationStatus;
  [DeviationGroupBy.CREATION_DATE_WEEK]: string;
}

export const DeviationGroupByFieldMetaMap: {
  [K in keyof DeviationGroupByFieldType]: EnumMetaMap<DeviationGroupByFieldType[K]>;
} = {
  [DeviationGroupBy.STATUS]: DeviationStatusMeta,
  [DeviationGroupBy.CREATION_DATE_WEEK]: CreationDateWeekMeta,
};

export const DeviationGroupByFieldSortFunctionMap = {
  [DeviationGroupBy.CREATION_DATE_WEEK]: sortDates,
};

export enum DeviationSortField {
  SID = 'sid',
  DESCRIPTION = 'description',
  STATUS = 'status',
  DATE = 'date',
}

export const DeviationFieldSortMap: Partial<Record<keyof DeviationRead, DeviationSortField>> = {
  sid: DeviationSortField.SID,
  description: DeviationSortField.DESCRIPTION,
  status: DeviationSortField.STATUS,
  date: DeviationSortField.DATE,
};

export interface DeviationSort {
  field: DeviationSortField;
  direction: 'asc' | 'desc';
}

export enum DeviationColumn {
  SID = 'sid',
  DESCRIPTION = 'description',
  GROUP = 'group',
  CREATED_BY = 'createdBy',
  LOCATION = 'location',
  STATUS = 'status',
  DATE = 'date',
}

export const DeviationColumnDisplayMap: Record<DeviationColumn, string> = {
  [DeviationColumn.SID]: 'ID',
  [DeviationColumn.DESCRIPTION]: 'Description',
  [DeviationColumn.GROUP]: 'Group',
  [DeviationColumn.CREATED_BY]: 'Created by',
  [DeviationColumn.LOCATION]: 'Location',
  [DeviationColumn.STATUS]: 'Status',
  [DeviationColumn.DATE]: 'Date',
};

export interface DeviationColumnSetting {
  column: DeviationColumn;
  hidden: boolean;
  width: number;
}

export const DeviationColumnDefaults: DeviationColumnSetting[] = [
  {
    column: DeviationColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: DeviationColumn.DESCRIPTION,
    hidden: false,
    width: 400,
  },
  {
    column: DeviationColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: DeviationColumn.CREATED_BY,
    hidden: false,
    width: 200,
  },
  {
    column: DeviationColumn.LOCATION,
    hidden: false,
    width: 200,
  },
  {
    column: DeviationColumn.STATUS,
    hidden: false,
    width: 180,
  },
  {
    column: DeviationColumn.DATE,
    hidden: false,
    width: 220,
  },
];
