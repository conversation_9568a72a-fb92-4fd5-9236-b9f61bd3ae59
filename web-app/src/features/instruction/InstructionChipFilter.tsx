import React, { useState, useEffect } from 'react';
import { <PERSON>, Stack, IconButton, Popover, FormControlLabel, Tooltip, Typography, Checkbox } from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { useAppDispatch, useAppSelector } from '../../store';
import { setInstructionViewState } from './instructionSlice';
import { User } from '../user/userTypes';
import {
  InstructionCandidateGroup,
  InstructionCandidateGroupDisplayMap,
  InstructionColumn,
  InstructionColumnDefaults,
  InstructionColumnDisplayMap,
  InstructionColumnSetting,
} from './instructionTypes';

const mergeColumns = (
  persistedColumns: InstructionColumnSetting[] = [],
  defaultColumns: InstructionColumnSetting[] = []
): InstructionColumnSetting[] => {
  // Keep only the persisted columns that still exist in the defaults.
  const validPersisted = persistedColumns.filter((pc) => defaultColumns.some((dc) => dc.column === pc.column));

  // Create a set of the column keys in the valid persisted list.
  const persistedSet = new Set(validPersisted.map((pc) => pc.column));

  // Find any default columns that the customer hasn't chosen yet.
  const newDefaults = defaultColumns.filter((dc) => !persistedSet.has(dc.column));

  // Return the persisted order first, then add new columns at the end.
  return [...validPersisted, ...newDefaults];
};

interface InstructionChipFilterProps {
  me?: User;
  resetPageNumber: () => void;
}

function InstructionChipFilter({ me, resetPageNumber }: InstructionChipFilterProps) {
  const instructionViewState = useAppSelector((state) => state.instruction.instructionViewState);
  const dispatch = useAppDispatch();

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
  const [columnsOrder, setColumnsOrder] = useState<InstructionColumnSetting[]>(
    mergeColumns(instructionViewState.columns, InstructionColumnDefaults)
  );

  useEffect(() => {
    setColumnsOrder(mergeColumns(instructionViewState.columns, InstructionColumnDefaults));
  }, [instructionViewState.columns]);

  const handleFineTuneClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'column-popover' : undefined;

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      // Update view state with the new ordering.
      dispatch(setInstructionViewState({ ...instructionViewState, columns: newOrder }));
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: InstructionColumn) => {
    // Toggle the hidden flag for the selected column.
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    dispatch(setInstructionViewState({ ...instructionViewState, columns: newOrder }));
  };

  return (
    <Stack direction="row" flexWrap={{ xs: 'nowrap', sm: 'wrap' }} overflow={{ xs: 'scroll', sm: 'unset' }}>
      <Chip
        label="Created by me"
        sx={{ mb: 1, mr: 1 }}
        color={instructionViewState.createdBy && instructionViewState.createdBy.id === me?.id ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...instructionViewState };
          if (newState.createdBy && newState.createdBy.id === me?.id) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          dispatch(setInstructionViewState(newState));
          resetPageNumber();
        }}
      />
      {Object.values(InstructionCandidateGroup).map((g) => (
        <Chip
          sx={{ mb: 1, mr: 1 }}
          label={InstructionCandidateGroupDisplayMap[g]}
          color={instructionViewState.candidateGroups?.find((c) => c === g) ? 'primary' : 'default'}
          onClick={() => {
            const newState = { ...instructionViewState };
            if (!!newState.candidateGroups && !!newState.candidateGroups.find((c) => c === g)) {
              newState.candidateGroups = newState.candidateGroups.filter((c) => c !== g);
            } else {
              const newGroups = newState.candidateGroups || [];
              newState.candidateGroups = newGroups.concat(g);
            }
            dispatch(setInstructionViewState(newState));
            resetPageNumber();
          }}
        />
      ))}
      <Tooltip title="View options">
        <IconButton size="small" sx={{ p: 0.5, mb: 1, mr: 1 }} onClick={handleFineTuneClick}>
          <TuneIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              px: 2,
              py: 1,
              maxWidth: 300,
            },
          },
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Columns</Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={InstructionColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}

export default InstructionChipFilter;
