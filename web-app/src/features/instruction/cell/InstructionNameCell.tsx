import { GridRenderCellParams } from '@mui/x-data-grid';
import { InstructionRead } from '../instructionTypes';

function InstructionNameCell(params: GridRenderCellParams<InstructionRead, string, string>) {
  return (
    <div
      style={{
        whiteSpace: 'pre-wrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        WebkitLineClamp: 3,
        WebkitBoxOrient: 'vertical',
      }}
    >
      {params.value}
    </div>
  );
}

export default InstructionNameCell;
