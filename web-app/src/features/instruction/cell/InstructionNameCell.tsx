import { GridRenderCellParams } from '@mui/x-data-grid';
import { InstructionRead } from '../instructionTypes';

function InstructionNameCell({ value }: GridRenderCellParams<InstructionRead, string, string>) {
  return (
    <div
      style={{
        whiteSpace: 'pre-wrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        WebkitLineClamp: 3,
        WebkitBoxOrient: 'vertical',
      }}
    >
      {value}
    </div>
  );
}

export default InstructionNameCell;
