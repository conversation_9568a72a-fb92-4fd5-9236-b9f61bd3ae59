import { Box } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import { InstructionRead, InstructionStatus, InstructionStatusDisplayMap } from '../instructionTypes';

function InstructionStatusCell(params: GridRenderCellParams<InstructionRead, InstructionStatus, string>) {
  const instruction = params.row;
  const status = params.value;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
      {instruction?.locked ? (
        <LockIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
      ) : (
        <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
      )}
      {status ? InstructionStatusDisplayMap[status] : ''}
    </Box>
  );
}

export default InstructionStatusCell;
