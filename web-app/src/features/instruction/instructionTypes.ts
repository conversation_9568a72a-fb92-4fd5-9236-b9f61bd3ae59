import { Dayjs } from 'dayjs';
import { GroupDisplay } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { FileDisplay } from '../file/fileTypes';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';

export interface InstructionParams {
  groupId?: number;
  ancestorGroupId?: number;
  pathGroupId?: number;
  startDateLte?: number;
  startDateGte?: number;
  endDateLte?: number;
  endDateGte?: number;
  endDateNotNull?: boolean;
  endDateNull?: boolean;
  status?: InstructionStatus;
  statusNot?: InstructionStatus;
  candidateGroups?: InstructionCandidateGroup[];
  createdBy?: number;
  filter?: string;
  search?: string;
  sort?: string;
  pageNumber?: number;
  pageSize?: number;
}

export interface InstructionPdfParams {
  id: number;
  timeZone: string;
}

export interface InstructionFormInput {
  group: GroupDisplay | null;
  description: string;
  startTime: Dayjs;
  endTime: Dayjs | null;
  files: FileDisplay[];
}

export interface InstructionViewState {
  listView: 'mine' | 'all';
  group?: GroupDisplay;
  status?: InstructionStatus;
  search?: string;
  candidateGroups?: InstructionCandidateGroup[];
  createdBy?: User;
  sort?: InstructionSort[];
  columns?: InstructionColumnSetting[];
}

export interface InstructionState {
  instructionCopy?: Partial<InstructionCopy>;
  instructionViewState: InstructionViewState;
}

export enum InstructionCandidateGroup {
  INSTRUCTION_CLOSE = 'INSTRUCTION_CLOSE',
}

export const InstructionCandidateGroupDisplayMap: Record<InstructionCandidateGroup, string> = {
  INSTRUCTION_CLOSE: 'Close',
};

export interface InstructionCreate {
  description: string;
  group: number;
  startTime: number;
  endTime?: number;
  files: number[];
}
export enum InstructionStatus {
  PLANNED = 'PLANNED',
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const InstructionStatusMeta: EnumMetaMap<InstructionStatus> = {
  [InstructionStatus.PLANNED]: { label: 'Planned', color: themeToColor('primary.main') },
  [InstructionStatus.ACTIVE]: { label: 'Active', color: themeToColor('secondary.main') },
  [InstructionStatus.CLOSED]: { label: 'Closed', color: themeToColor('success.main') },
  [InstructionStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const InstructionStatusDisplayMap = Object.fromEntries(
  Object.entries(InstructionStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<InstructionStatus, string>;

export interface InstructionRead {
  id: number;
  sid: number;
  description: string;
  group: GroupDisplay;
  status: InstructionStatus;
  processInstanceId: string;
  startTime: number;
  endTime: number;
  locked: boolean;
  createdBy: UserDisplay;
  files: FileDisplay[];
}

export interface InstructionCopy {
  description: string;
}

export interface InstructionUpdate {
  id: number;
  description?: string;
  startTime?: number;
  endTime?: number;
  files: number[];
}

export interface InstructionDeletable {
  deletable: boolean;
}

export enum InstructionChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const InstructionChangeTypeDisplayMap: Record<InstructionChangeType, string> = {
  INSERT: 'Instruction created',
  UPDATE: 'Instruction updated',
  DELETE: 'Instruction deleted',
};

export interface InstructionChange {
  by: UserDisplay;
  at: number;
  type: InstructionChangeType;
  oldEntity: InstructionRead;
  newEntity: InstructionRead;
}

export enum InstructionGroupBy {
  GROUP = 'INSTRUCTIONS_GROUP',
  CREATED_BY = 'INSTRUCTIONS_CREATED_BY',
  STATUS = 'INSTRUCTIONS_STATUS',
  CREATION_DATE_WEEK = 'INSTRUCTIONS_CREATION_DATE_WEEK',
}

export const InstructionGroupByDisplayMap: Record<InstructionGroupBy, string> = {
  [InstructionGroupBy.GROUP]: 'Group',
  [InstructionGroupBy.CREATED_BY]: 'User',
  [InstructionGroupBy.STATUS]: 'Status',
  [InstructionGroupBy.CREATION_DATE_WEEK]: 'Instruction week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface InstructionGroupByFieldType {
  [InstructionGroupBy.STATUS]: InstructionStatus;
  [InstructionGroupBy.CREATION_DATE_WEEK]: string;
}

export const InstructionGroupByFieldMetaMap: {
  [K in keyof InstructionGroupByFieldType]: EnumMetaMap<InstructionGroupByFieldType[K]>;
} = {
  [InstructionGroupBy.STATUS]: InstructionStatusMeta,
  [InstructionGroupBy.CREATION_DATE_WEEK]: CreationDateWeekMeta,
};

export const InstructionGroupByFieldSortFunctionMap = {
  [InstructionGroupBy.CREATION_DATE_WEEK]: sortDates,
};

export enum InstructionSortField {
  SID = 'sid',
  DESCRIPTION = 'description',
  STATUS = 'status',
  START_TIME = 'startTime',
}

export const InstructionFieldSortMap: Partial<Record<keyof InstructionRead, InstructionSortField>> = {
  sid: InstructionSortField.SID,
  description: InstructionSortField.DESCRIPTION,
  status: InstructionSortField.STATUS,
  startTime: InstructionSortField.START_TIME,
};

export interface InstructionSort {
  field: InstructionSortField;
  direction: 'asc' | 'desc';
}

export enum InstructionColumn {
  SID = 'sid',
  DESCRIPTION = 'description',
  GROUP = 'group',
  CREATED_BY = 'createdBy',
  STATUS = 'status',
  DATE = 'date',
}

export const InstructionColumnDisplayMap: Record<InstructionColumn, string> = {
  [InstructionColumn.SID]: 'ID',
  [InstructionColumn.DESCRIPTION]: 'Description',
  [InstructionColumn.GROUP]: 'Group',
  [InstructionColumn.CREATED_BY]: 'Created by',
  [InstructionColumn.STATUS]: 'Status',
  [InstructionColumn.DATE]: 'Date',
};

export interface InstructionColumnSetting {
  column: InstructionColumn;
  hidden: boolean;
  width: number;
}

export const InstructionColumnDefaults: InstructionColumnSetting[] = [
  {
    column: InstructionColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: InstructionColumn.DESCRIPTION,
    hidden: false,
    width: 400,
  },
  {
    column: InstructionColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: InstructionColumn.CREATED_BY,
    hidden: false,
    width: 200,
  },
  {
    column: InstructionColumn.STATUS,
    hidden: false,
    width: 120,
  },
  {
    column: InstructionColumn.DATE,
    hidden: false,
    width: 405,
  },
];
