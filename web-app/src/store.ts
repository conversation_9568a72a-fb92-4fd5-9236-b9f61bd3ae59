import { combineReducers, configureStore, Reducer } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { FLUSH, PAUSE, PERSIST, persistStore, PURGE, REGISTER, REHYDRATE } from 'redux-persist';
import { api } from './api';
import { authSlice, authReducer } from './features/auth/authSlice';
import { toolbarSlice } from './features/toolbar/toolbarSlice';
import { titleSlice } from './features/title/titleSlice';
import { workPermitReducer, workPermitSlice } from './features/work-permit/workPermitSlice';
import { traReducer, traSlice } from './features/tra/traSlice';
import { observationReducer, observationSlice } from './features/observation/observationSlice';
import { safetyWalkReducer, safetyWalkSlice } from './features/safety-walk/safetyWalkSlice';
import { incidentReducer, incidentSlice } from './features/incident/incidentSlice';
import { documentReducer, documentSlice } from './features/documents/documentSlice';
import { timesheetReducer, timesheetSlice } from './features/timesheet/timesheetSlice';
import { membershipReducer, membershipSlice } from './features/membership/membershipSlice';
import { shiftReportReducer, shiftReportSlice } from './features/shift-report/shiftReportSlice';
import { shiftLogReducer, shiftLogSlice } from './features/shift-report/log/shiftLogSlice';
import { deviationReducer, deviationSlice } from './features/deviation/deviationSlice';
import { instructionReducer, instructionSlice } from './features/instruction/instructionSlice';
import { taskReducer, taskSlice } from './features/task/taskSlice';
import { checklistAnswerReducer, checklistAnswerSlice } from './features/checklist/answer/checklistAnswerSlice';
import { changeReducer, changeSlice } from './features/change/changeSlice';
import { checklistReducer, checklistSlice } from './features/checklist/checklistSlice';
import { findingReducer, findingSlice } from './features/finding/findingSlice';
import { externalAuditReducer, externalAuditSlice } from './features/auditexternal/externalAuditSlice';
import { internalAuditReducer, internalAuditSlice } from './features/auditinternal/internalAuditSlice';
import { crossReferenceReducer, crossReferenceSlice } from './features/norm/crossReferenceSlice';
import { userReducer, userSlice } from './features/user/userSlice';
import { roleReducer, roleSlice } from './features/role/roleSlice';
import { groupReducer, groupSlice } from './features/group/groupSlice';
import { locationSlice } from './features/location/locationSlice';
import { ruleReducer, ruleSlice } from './features/work-permit/rule/ruleSlice';
import { lototoReducer, lototoSlice } from './features/lototo/lototoSlice';
import { lototoItemReducer, lototoItemSlice } from './features/lototo/item/lototoSlice';
import { lototoMethodReducer, lototoMethodSlice } from './features/lototo/method/lototoMethodSlice';
import { folderReducer, folderSlice } from './features/folder/folderSlice';
import { dashboardReducer, dashboardSlice } from './features/dashboard/dashboardSlice';

const reducers = {
  [api.reducerPath]: api.reducer,
  [authSlice.name]: authReducer,
  [toolbarSlice.name]: toolbarSlice.reducer,
  [titleSlice.name]: titleSlice.reducer,
  [taskSlice.name]: taskReducer,
  [workPermitSlice.name]: workPermitReducer,
  [traSlice.name]: traReducer,
  [observationSlice.name]: observationReducer,
  [safetyWalkSlice.name]: safetyWalkReducer,
  [incidentSlice.name]: incidentReducer,
  [timesheetSlice.name]: timesheetReducer,
  [documentSlice.name]: documentReducer,
  [membershipSlice.name]: membershipReducer,
  [shiftReportSlice.name]: shiftReportReducer,
  [shiftLogSlice.name]: shiftLogReducer,
  [deviationSlice.name]: deviationReducer,
  [instructionSlice.name]: instructionReducer,
  [checklistAnswerSlice.name]: checklistAnswerReducer,
  [changeSlice.name]: changeReducer,
  [checklistSlice.name]: checklistReducer,
  [findingSlice.name]: findingReducer,
  [externalAuditSlice.name]: externalAuditReducer,
  [internalAuditSlice.name]: internalAuditReducer,
  [crossReferenceSlice.name]: crossReferenceReducer,
  [userSlice.name]: userReducer,
  [roleSlice.name]: roleReducer,
  [groupSlice.name]: groupReducer,
  [locationSlice.name]: locationSlice.reducer,
  [ruleSlice.name]: ruleReducer,
  [lototoSlice.name]: lototoReducer,
  [lototoItemSlice.name]: lototoItemReducer,
  [lototoMethodSlice.name]: lototoMethodReducer,
  [folderSlice.name]: folderReducer,
  [dashboardSlice.name]: dashboardReducer,
};

const combinedReducer = combineReducers<typeof reducers>(reducers);

export const rootReducer: Reducer<RootState> = (state, action) => {
  if (action.type === 'reset') {
    return combinedReducer(undefined, action);
  }

  return combinedReducer(state, action);
};

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(api.middleware),
});

export const persistor = persistStore(store);

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof combinedReducer>;
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// optional, but required for refetchOnFocus/refetchOnReconnect behaviors
// see `setupListeners` docs - takes an optional callback as the 2nd arg for customization
setupListeners(store.dispatch);
